package com.tqhit.battery.one.manager.animation

import android.content.Context
import com.tqhit.battery.one.fragment.main.animation.data.PreloadStatus
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import kotlinx.coroutines.test.runTest
import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import java.io.File

/**
 * Unit tests for AnimationFileManager.
 * Tests file operations, validation, and cleanup functionality.
 * 
 * Following Arrange-Act-Assert convention for test structure.
 */
class AnimationFileManagerTest {
    
    // Test dependencies
    private lateinit var mockContext: Context
    private lateinit var mockFilesDir: File
    private lateinit var mockPreloadDir: File
    private lateinit var animationFileManager: AnimationFileManager
    
    @Before
    fun setUp() {
        // Arrange - Set up mocks and test subject
        mockContext = mockk(relaxed = true)
        mockFilesDir = mockk(relaxed = true)
        mockPreloadDir = mockk(relaxed = true)
        
        every { mockContext.filesDir } returns mockFilesDir
        every { mockFilesDir.absolutePath } returns "/mock/files"
        every { mockPreloadDir.exists() } returns true
        every { mockPreloadDir.mkdirs() } returns true
        
        // Mock File constructor for preload directory
        mockkStatic(File::class)
        every { File(mockFilesDir, "preloaded_animations") } returns mockPreloadDir
        
        animationFileManager = AnimationFileManager(mockContext)
    }
    
    @After
    fun tearDown() {
        unmockkAll()
    }
    
    @Test
    fun `generateFileName should create valid filename from URL`() {
        // Arrange
        val inputUrl = "https://example.com/animation.mp4"
        
        // Act
        val actualFileName = animationFileManager.generateFileName(inputUrl)
        
        // Assert
        assertTrue("Filename should end with .mp4", actualFileName.endsWith(".mp4"))
        assertTrue("Filename should not be empty", actualFileName.isNotEmpty())
        assertTrue("Filename should be reasonable length", actualFileName.length <= 104) // 100 + ".mp4"
    }
    
    @Test
    fun `generateFileName should handle long URLs`() {
        // Arrange
        val longUrl = "https://example.com/" + "a".repeat(200) + ".mp4"
        
        // Act
        val actualFileName = animationFileManager.generateFileName(longUrl)
        
        // Assert
        assertTrue("Long URL filename should end with .mp4", actualFileName.endsWith(".mp4"))
        assertTrue("Long URL filename should be truncated", actualFileName.length <= 104)
    }
    
    @Test
    fun `generateFileName should handle special characters`() {
        // Arrange
        val urlWithSpecialChars = "https://example.com/animation with spaces & symbols!.mp4"
        
        // Act
        val actualFileName = animationFileManager.generateFileName(urlWithSpecialChars)
        
        // Assert
        assertTrue("Special chars filename should end with .mp4", actualFileName.endsWith(".mp4"))
        assertFalse("Filename should not contain spaces", actualFileName.contains(" "))
        assertFalse("Filename should not contain special chars", actualFileName.contains("&"))
    }
    
    @Test
    fun `getPreloadedFile should return null when file does not exist`() = runTest {
        // Arrange
        val mediaUrl = "https://example.com/test.mp4"
        val mockFile = mockk<File>(relaxed = true)
        
        every { File(mockPreloadDir, any<String>()) } returns mockFile
        every { mockFile.exists() } returns false
        
        // Act
        val actualResult = animationFileManager.getPreloadedFile(mediaUrl)
        
        // Assert
        assertNull("Should return null when file does not exist", actualResult)
    }
    
    @Test
    fun `getPreloadedFile should return PreloadedAnimationItem when file exists`() = runTest {
        // Arrange
        val mediaUrl = "https://example.com/test.mp4"
        val mockFile = mockk<File>(relaxed = true)
        val expectedPath = "/mock/preload/test.mp4"
        val expectedSize = 1024L
        val expectedTimestamp = System.currentTimeMillis()
        
        every { File(mockPreloadDir, any<String>()) } returns mockFile
        every { mockFile.exists() } returns true
        every { mockFile.isFile } returns true
        every { mockFile.length() } returns expectedSize
        every { mockFile.absolutePath } returns expectedPath
        every { mockFile.lastModified() } returns expectedTimestamp
        
        // Act
        val actualResult = animationFileManager.getPreloadedFile(mediaUrl)
        
        // Assert
        assertNotNull("Should return PreloadedAnimationItem when file exists", actualResult)
        assertEquals("Media URL should match", mediaUrl, actualResult?.mediaOriginal)
        assertEquals("File path should match", expectedPath, actualResult?.localFilePath)
        assertEquals("File size should match", expectedSize, actualResult?.fileSizeBytes)
        assertEquals("Status should be COMPLETED", PreloadStatus.COMPLETED, actualResult?.status)
    }
    
    @Test
    fun `getPreloadedFile should return EXPIRED status for old files`() = runTest {
        // Arrange
        val mediaUrl = "https://example.com/test.mp4"
        val mockFile = mockk<File>(relaxed = true)
        val oldTimestamp = System.currentTimeMillis() - (8 * 24 * 60 * 60 * 1000L) // 8 days ago
        
        every { File(mockPreloadDir, any<String>()) } returns mockFile
        every { mockFile.exists() } returns true
        every { mockFile.isFile } returns true
        every { mockFile.length() } returns 1024L
        every { mockFile.lastModified() } returns oldTimestamp
        every { mockFile.absolutePath } returns "/mock/test.mp4"
        
        // Act
        val actualResult = animationFileManager.getPreloadedFile(mediaUrl)
        
        // Assert
        assertNotNull("Should return PreloadedAnimationItem for old file", actualResult)
        assertEquals("Status should be EXPIRED for old file", PreloadStatus.EXPIRED, actualResult?.status)
    }
    
    @Test
    fun `createPreloadFile should return File object`() {
        // Arrange
        val mediaUrl = "https://example.com/test.mp4"
        val mockFile = mockk<File>(relaxed = true)
        
        every { File(mockPreloadDir, any<String>()) } returns mockFile
        
        // Act
        val actualFile = animationFileManager.createPreloadFile(mediaUrl)
        
        // Assert
        assertNotNull("Should return File object", actualFile)
        assertEquals("Should return the mocked file", mockFile, actualFile)
    }
    
    @Test
    fun `validateDownloadedFile should return false for non-existent file`() = runTest {
        // Arrange
        val mockFile = mockk<File>(relaxed = true)
        every { mockFile.exists() } returns false
        
        // Act
        val actualResult = animationFileManager.validateDownloadedFile(mockFile)
        
        // Assert
        assertFalse("Should return false for non-existent file", actualResult)
    }
    
    @Test
    fun `validateDownloadedFile should return false for empty file`() = runTest {
        // Arrange
        val mockFile = mockk<File>(relaxed = true)
        every { mockFile.exists() } returns true
        every { mockFile.isFile } returns true
        every { mockFile.length() } returns 0L
        
        // Act
        val actualResult = animationFileManager.validateDownloadedFile(mockFile)
        
        // Assert
        assertFalse("Should return false for empty file", actualResult)
    }
    
    @Test
    fun `cleanupOldFiles should return count of cleaned files`() = runTest {
        // Arrange
        val mockFile1 = mockk<File>(relaxed = true)
        val mockFile2 = mockk<File>(relaxed = true)
        val mockFiles = arrayOf(mockFile1, mockFile2)
        
        every { mockPreloadDir.listFiles() } returns mockFiles
        every { mockFile1.isFile } returns true
        every { mockFile1.length() } returns 0L // Empty file - should be deleted
        every { mockFile1.delete() } returns true
        every { mockFile2.isFile } returns true
        every { mockFile2.length() } returns 1024L
        every { mockFile2.lastModified() } returns System.currentTimeMillis() // Recent file
        
        // Act
        val actualCount = animationFileManager.cleanupOldFiles()
        
        // Assert
        assertEquals("Should return count of cleaned files", 1, actualCount)
    }
    
    @Test
    fun `getTotalPreloadedSize should return sum of file sizes`() = runTest {
        // Arrange
        val mockFile1 = mockk<File>(relaxed = true)
        val mockFile2 = mockk<File>(relaxed = true)
        val mockFiles = arrayOf(mockFile1, mockFile2)
        val expectedSize1 = 1024L
        val expectedSize2 = 2048L
        val expectedTotalSize = expectedSize1 + expectedSize2
        
        every { mockPreloadDir.listFiles() } returns mockFiles
        every { mockFile1.isFile } returns true
        every { mockFile1.length() } returns expectedSize1
        every { mockFile2.isFile } returns true
        every { mockFile2.length() } returns expectedSize2
        
        // Act
        val actualTotalSize = animationFileManager.getTotalPreloadedSize()
        
        // Assert
        assertEquals("Should return sum of file sizes", expectedTotalSize, actualTotalSize)
    }
    
    @Test
    fun `getPreloadedFileCount should return count of valid files`() = runTest {
        // Arrange
        val mockFile1 = mockk<File>(relaxed = true)
        val mockFile2 = mockk<File>(relaxed = true)
        val mockFile3 = mockk<File>(relaxed = true)
        val mockFiles = arrayOf(mockFile1, mockFile2, mockFile3)
        
        every { mockPreloadDir.listFiles() } returns mockFiles
        every { mockFile1.isFile } returns true
        every { mockFile1.length() } returns 1024L
        every { mockFile2.isFile } returns true
        every { mockFile2.length() } returns 0L // Empty file - should not be counted
        every { mockFile3.isFile } returns false // Directory - should not be counted
        
        // Act
        val actualCount = animationFileManager.getPreloadedFileCount()
        
        // Assert
        assertEquals("Should return count of valid files only", 1, actualCount)
    }

    @Test
    fun `generateFileName should handle exception gracefully`() {
        // Arrange - This test ensures fallback behavior works
        val problematicUrl = ""

        // Act
        val actualFileName = animationFileManager.generateFileName(problematicUrl)

        // Assert
        assertTrue("Should generate fallback filename", actualFileName.startsWith("animation_"))
        assertTrue("Fallback filename should end with .mp4", actualFileName.endsWith(".mp4"))
    }
}
