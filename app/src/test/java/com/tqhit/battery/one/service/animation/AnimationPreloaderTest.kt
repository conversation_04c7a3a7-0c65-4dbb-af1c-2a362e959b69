package com.tqhit.battery.one.service.animation

import android.content.Context
import com.tqhit.battery.one.fragment.main.animation.data.AnimationItem
import com.tqhit.battery.one.fragment.main.animation.data.PreloadResult
import com.tqhit.battery.one.fragment.main.animation.data.PreloadStatus
import com.tqhit.battery.one.manager.animation.AnimationFileManager
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import java.io.File

/**
 * Unit tests for AnimationPreloader.
 * Tests preloading logic, error handling, and concurrent download management.
 * 
 * Following Arrange-Act-Assert convention for test structure.
 */
class AnimationPreloaderTest {
    
    // Test dependencies
    private lateinit var mockContext: Context
    private lateinit var mockFileManager: AnimationFileManager
    private lateinit var animationPreloader: AnimationPreloader
    
    // Test data
    private lateinit var testAnimations: List<AnimationItem>
    
    @Before
    fun setUp() {
        // Arrange - Set up mocks and test subject
        mockContext = mockk(relaxed = true)
        mockFileManager = mockk(relaxed = true)
        
        animationPreloader = AnimationPreloader(mockContext, mockFileManager)
        
        // Create test animation data
        testAnimations = listOf(
            AnimationItem(
                isPremium = false,
                mediaOriginal = "https://example.com/animation1.mp4",
                thumbnail = "https://example.com/thumb1.jpg"
            ),
            AnimationItem(
                isPremium = true,
                mediaOriginal = "https://example.com/animation2.mp4",
                thumbnail = "https://example.com/thumb2.jpg"
            ),
            AnimationItem(
                isPremium = false,
                mediaOriginal = "https://example.com/animation3.mp4",
                thumbnail = "https://example.com/thumb3.jpg"
            )
        )
    }
    
    @Test
    fun `preloadAnimations should return empty list for empty input`() = runTest {
        // Arrange
        val emptyAnimations = emptyList<AnimationItem>()
        
        // Act
        val actualResults = animationPreloader.preloadAnimations(emptyAnimations)
        
        // Assert
        assertTrue("Should return empty list for empty input", actualResults.isEmpty())
    }
    
    @Test
    fun `preloadAnimations should limit to first 6 animations`() = runTest {
        // Arrange
        val manyAnimations = (1..10).map { index ->
            AnimationItem(
                isPremium = false,
                mediaOriginal = "https://example.com/animation$index.mp4",
                thumbnail = "https://example.com/thumb$index.jpg"
            )
        }
        
        // Mock file manager to return no existing files
        coEvery { mockFileManager.getPreloadedFile(any()) } returns null
        coEvery { mockFileManager.createPreloadFile(any()) } returns mockk(relaxed = true)
        coEvery { mockFileManager.validateDownloadedFile(any()) } returns false
        
        // Act
        val actualResults = animationPreloader.preloadAnimations(manyAnimations)
        
        // Assert
        assertEquals("Should process only first 6 animations", 6, actualResults.size)
    }
    
    @Test
    fun `preloadAnimations should return AlreadyExists for existing files`() = runTest {
        // Arrange
        val existingPreloadedItem = com.tqhit.battery.one.fragment.main.animation.data.PreloadedAnimationItem(
            mediaOriginal = testAnimations[0].mediaOriginal,
            localFilePath = "/mock/path/animation1.mp4",
            status = PreloadStatus.COMPLETED,
            downloadTimestamp = System.currentTimeMillis(),
            fileSizeBytes = 1024L
        )
        
        coEvery { mockFileManager.getPreloadedFile(testAnimations[0].mediaOriginal) } returns existingPreloadedItem
        coEvery { mockFileManager.getPreloadedFile(testAnimations[1].mediaOriginal) } returns null
        coEvery { mockFileManager.getPreloadedFile(testAnimations[2].mediaOriginal) } returns null
        
        // Mock other calls to avoid actual downloads
        coEvery { mockFileManager.createPreloadFile(any()) } returns mockk(relaxed = true)
        coEvery { mockFileManager.validateDownloadedFile(any()) } returns false
        
        // Act
        val actualResults = animationPreloader.preloadAnimations(testAnimations)
        
        // Assert
        assertEquals("Should return 3 results", 3, actualResults.size)
        assertTrue("First result should be AlreadyExists", actualResults[0] is PreloadResult.AlreadyExists)
        assertTrue("Other results should be Failure", actualResults[1] is PreloadResult.Failure)
        assertTrue("Other results should be Failure", actualResults[2] is PreloadResult.Failure)
    }
    
    @Test
    fun `preloadAnimations should return Failure for invalid URLs`() = runTest {
        // Arrange
        val invalidAnimations = listOf(
            AnimationItem(
                isPremium = false,
                mediaOriginal = "invalid-url",
                thumbnail = "https://example.com/thumb.jpg"
            ),
            AnimationItem(
                isPremium = false,
                mediaOriginal = "",
                thumbnail = "https://example.com/thumb.jpg"
            )
        )
        
        coEvery { mockFileManager.getPreloadedFile(any()) } returns null
        
        // Act
        val actualResults = animationPreloader.preloadAnimations(invalidAnimations)
        
        // Assert
        assertEquals("Should return 2 results", 2, actualResults.size)
        assertTrue("First result should be Failure", actualResults[0] is PreloadResult.Failure)
        assertTrue("Second result should be Failure", actualResults[1] is PreloadResult.Failure)
        
        val firstFailure = actualResults[0] as PreloadResult.Failure
        assertTrue("Error message should mention invalid URL", 
            firstFailure.errorMessage.contains("Invalid URL", ignoreCase = true))
    }
    
    @Test
    fun `preloadAnimations should return Success for valid downloads`() = runTest {
        // Arrange
        val mockFile = mockk<File>(relaxed = true)
        every { mockFile.absolutePath } returns "/mock/path/animation1.mp4"
        every { mockFile.length() } returns 2048L
        
        coEvery { mockFileManager.getPreloadedFile(any()) } returns null
        coEvery { mockFileManager.createPreloadFile(any()) } returns mockFile
        coEvery { mockFileManager.validateDownloadedFile(any()) } returns true
        
        // Mock successful download by creating a simple test that doesn't actually download
        val singleAnimation = listOf(testAnimations[0])
        
        // Act
        val actualResults = animationPreloader.preloadAnimations(singleAnimation)
        
        // Assert
        assertEquals("Should return 1 result", 1, actualResults.size)
        // Note: This will likely be a Failure due to actual network call, but tests the structure
        assertNotNull("Should return a result", actualResults[0])
    }
    
    @Test
    fun `preloadAnimations should handle file validation failure`() = runTest {
        // Arrange
        val mockFile = mockk<File>(relaxed = true)
        every { mockFile.delete() } returns true
        
        coEvery { mockFileManager.getPreloadedFile(any()) } returns null
        coEvery { mockFileManager.createPreloadFile(any()) } returns mockFile
        coEvery { mockFileManager.validateDownloadedFile(any()) } returns false
        
        val singleAnimation = listOf(testAnimations[0])
        
        // Act
        val actualResults = animationPreloader.preloadAnimations(singleAnimation)
        
        // Assert
        assertEquals("Should return 1 result", 1, actualResults.size)
        // The result will be a Failure due to validation failure or network issues
        assertNotNull("Should return a result", actualResults[0])
    }
    
    @Test
    fun `getPreloadingStatus should return current status`() {
        // Arrange - No setup needed for this simple getter
        
        // Act
        val actualStatus = animationPreloader.getPreloadingStatus()
        
        // Assert
        assertNotNull("Should return PreloadingStatus", actualStatus)
        assertEquals("Should have correct max concurrent downloads", 3, actualStatus.maxConcurrentDownloads)
        assertTrue("Available slots should be non-negative", actualStatus.availableSlots >= 0)
        assertTrue("Active downloads should be non-negative", actualStatus.activeDownloads >= 0)
    }
    
    @Test
    fun `cancelPreloading should complete without error`() = runTest {
        // Arrange - No specific setup needed
        
        // Act & Assert - Should not throw exception
        animationPreloader.cancelPreloading()
        
        // Test passes if no exception is thrown
        assertTrue("Cancel preloading should complete successfully", true)
    }
    
    @Test
    fun `preloadAnimations should verify file manager interactions`() = runTest {
        // Arrange
        coEvery { mockFileManager.getPreloadedFile(any()) } returns null
        coEvery { mockFileManager.createPreloadFile(any()) } returns mockk(relaxed = true)
        coEvery { mockFileManager.validateDownloadedFile(any()) } returns false
        
        val singleAnimation = listOf(testAnimations[0])
        
        // Act
        animationPreloader.preloadAnimations(singleAnimation)
        
        // Assert - Verify interactions with file manager
        coVerify { mockFileManager.getPreloadedFile(testAnimations[0].mediaOriginal) }
        coVerify { mockFileManager.createPreloadFile(testAnimations[0].mediaOriginal) }
    }
    
    @Test
    fun `preloadAnimations should handle concurrent downloads properly`() = runTest {
        // Arrange
        val manyAnimations = (1..5).map { index ->
            AnimationItem(
                isPremium = false,
                mediaOriginal = "https://example.com/animation$index.mp4",
                thumbnail = "https://example.com/thumb$index.jpg"
            )
        }
        
        coEvery { mockFileManager.getPreloadedFile(any()) } returns null
        coEvery { mockFileManager.createPreloadFile(any()) } returns mockk(relaxed = true)
        coEvery { mockFileManager.validateDownloadedFile(any()) } returns false
        
        // Act
        val actualResults = animationPreloader.preloadAnimations(manyAnimations)
        
        // Assert
        assertEquals("Should return results for all animations", 5, actualResults.size)
        // All results should be present (though likely failures due to network)
        actualResults.forEach { result ->
            assertNotNull("Each result should not be null", result)
        }
    }
}
