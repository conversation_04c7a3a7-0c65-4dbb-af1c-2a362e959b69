package com.tqhit.battery.one.fragment.main.others

import android.os.Bundle
import android.util.Log
import android.view.View
import androidx.fragment.app.viewModels
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.repeatOnLifecycle
import androidx.recyclerview.widget.LinearLayoutManager
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment
import com.tqhit.battery.one.R
import com.tqhit.battery.one.ads.core.ApplovinInterstitialAdManager
import com.tqhit.battery.one.databinding.FragmentOthersBinding
import com.tqhit.battery.one.dialog.capacity.SetupPasswordDialog
import com.tqhit.battery.one.dialog.utils.NotificationDialog
import com.tqhit.battery.one.features.navigation.DynamicNavigationManager
import com.tqhit.battery.one.features.navigation.AppNavigator
import com.tqhit.battery.one.features.navigation.SharedNavigationViewModel
import com.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider
import com.tqhit.battery.one.fragment.main.HealthFragment
import com.tqhit.battery.one.fragment.main.SettingsFragment
import com.tqhit.battery.one.fragment.main.others.adapter.OthersAdapter
import com.tqhit.battery.one.fragment.main.others.data.OthersItemData
import com.tqhit.battery.one.fragment.main.others.data.OthersItemData.Companion.CHARGE_DISCHARGE_ITEM_ID
import com.tqhit.battery.one.fragment.main.others.data.OthersItemData.Companion.HEALTH_ITEM_ID
import com.tqhit.battery.one.fragment.main.others.data.OthersItemData.Companion.SETTINGS_ITEM_ID
import com.tqhit.battery.one.viewmodel.AppViewModel
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Others fragment implementing card-based navigation for Charge/Discharge, Battery Health, and Settings.
 *
 * Features:
 * - Dynamic charge/discharge navigation based on real-time battery status
 * - CoreBatteryStatsService integration for battery state monitoring
 * - Anti-theft toggle functionality with password management
 * - MVI architecture pattern with proper state management
 * - Comprehensive logging for debugging and ADB testing
 *
 * Following the established stats module architecture pattern.
 */
@AndroidEntryPoint
class OthersFragment : AdLibBaseFragment<FragmentOthersBinding>() {

    override val binding by lazy { FragmentOthersBinding.inflate(layoutInflater) }

    // Modern architecture dependencies
    @Inject lateinit var coreBatteryStatsProvider: CoreBatteryStatsProvider
    @Inject lateinit var dynamicNavigationManager: DynamicNavigationManager
    @Inject lateinit var appNavigator: AppNavigator
    @Inject lateinit var applovinInterstitialAdManager: ApplovinInterstitialAdManager

    private val appViewModel: AppViewModel by viewModels()
    private val sharedNavigationViewModel: SharedNavigationViewModel by activityViewModels()
    private lateinit var othersAdapter: OthersAdapter
    private var isDeviceCharging: Boolean = false

    // CLICK_LISTENER_FIX: Click debouncing variables
    private var lastClickTime: Long = 0L

    // NAVIGATION_INVESTIGATION: Click counter and state tracking
    private var totalClickAttempts: Int = 0
    private var successfulNavigations: Int = 0
    private var failedNavigations: Int = 0
    private var lastNavigationSource: String = "UNKNOWN"
    private var fragmentCreationTime: Long = System.currentTimeMillis()
    private var lastBackNavigationTime: Long = 0L

    // BATTERY_OBSERVATION_FIX: Track battery observation state to prevent multiple concurrent observations
    private var isBatteryObservationActive: Boolean = false
    private var lastBatteryStateUpdateTime: Long = 0L

    // BUTTON_FLICKERING_FIX: Debounce UI updates to prevent rapid button state changes
    private var pendingUIUpdateJob: kotlinx.coroutines.Job? = null

    // BLANK_NAVIGATION_FIX: Navigation state protection to prevent corruption during UI updates
    private var isNavigationInProgress: Boolean = false
    private var navigationProtectionStartTime: Long = 0L

    companion object {
        private const val TAG = "OthersFragment"
        private const val CLICK_DEBOUNCE_DELAY = 500L // 500ms debounce delay
    }

    init {
        Log.d(TAG, "OTHERS_FRAGMENT: Fragment instance created")
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val viewCreatedTime = System.currentTimeMillis()

        // NAVIGATION_CHAIN_DEBUG: Enhanced logging for navigation chain investigation
        Log.d(TAG, "NAVIGATION_CHAIN_DEBUG: ═══════════════════════════════════════════════════════════")
        Log.d(TAG, "NAVIGATION_CHAIN_DEBUG: 🔄 OTHERS FRAGMENT VIEW CREATED")
        Log.d(TAG, "NAVIGATION_CHAIN_DEBUG: ═══════════════════════════════════════════════════════════")
        Log.d(TAG, "NAVIGATION_CHAIN_DEBUG: Timestamp: $viewCreatedTime")
        Log.d(TAG, "NAVIGATION_CHAIN_DEBUG: Fragment instance created at: $fragmentCreationTime")
        Log.d(TAG, "NAVIGATION_CHAIN_DEBUG: Time since fragment creation: ${viewCreatedTime - fragmentCreationTime}ms")
        Log.d(TAG, "NAVIGATION_CHAIN_DEBUG: Current charging state (cached): $isDeviceCharging")
        Log.d(TAG, "NAVIGATION_CHAIN_DEBUG: Last battery state update: $lastBatteryStateUpdateTime")
        Log.d(TAG, "NAVIGATION_CHAIN_DEBUG: Time since last battery update: ${viewCreatedTime - lastBatteryStateUpdateTime}ms")
        Log.d(TAG, "NAVIGATION_CHAIN_DEBUG: Battery observation active: $isBatteryObservationActive")

        // CRITICAL_FIX: Proactive battery state synchronization to prevent button text flicker
        val currentBatteryStatus = coreBatteryStatsProvider.getCurrentStatus()
        if (currentBatteryStatus != null) {
            Log.d(TAG, "NAVIGATION_CHAIN_DEBUG: Actual battery charging state: ${currentBatteryStatus.isCharging}")
            if (isDeviceCharging != currentBatteryStatus.isCharging) {
                Log.w(TAG, "NAVIGATION_CHAIN_DEBUG: *** STATE MISMATCH DETECTED AT VIEW CREATION ***")
                Log.w(TAG, "NAVIGATION_CHAIN_DEBUG: Cached: $isDeviceCharging, Actual: ${currentBatteryStatus.isCharging}")
                Log.w(TAG, "NAVIGATION_CHAIN_DEBUG: FIXING STATE IMMEDIATELY TO PREVENT FLICKER!")

                // CRITICAL_FIX: Synchronize state immediately to prevent flicker
                val previousState = isDeviceCharging
                isDeviceCharging = currentBatteryStatus.isCharging
                lastBatteryStateUpdateTime = viewCreatedTime

                Log.d(TAG, "NAVIGATION_CHAIN_DEBUG: State synchronized: $previousState → $isDeviceCharging")
                Log.d(TAG, "NAVIGATION_CHAIN_DEBUG: ✅ FLICKER PREVENTION: State corrected at view creation")
            } else {
                Log.d(TAG, "NAVIGATION_CHAIN_DEBUG: Battery state consistent at view creation")
            }
        } else {
            Log.w(TAG, "NAVIGATION_CHAIN_DEBUG: No current battery status available at view creation")
        }

        Log.d(TAG, "NAVIGATION_INVESTIGATION: === FRAGMENT VIEW CREATED ===")
        Log.d(TAG, "NAVIGATION_INVESTIGATION: Fragment instance created at: $fragmentCreationTime")
        Log.d(TAG, "NAVIGATION_INVESTIGATION: Resetting click counters for new view")

        // Reset counters for new view creation
        totalClickAttempts = 0
        successfulNavigations = 0
        failedNavigations = 0
        lastNavigationSource = "VIEW_CREATED"

        Log.d(TAG, "OTHERS_FRAGMENT: onViewCreated - initializing Others fragment")
        Log.d(TAG, "OTHERS_LIFECYCLE: Fragment view created, starting battery status observation")

        // Initialize AppNavigator for centralized navigation
        initializeAppNavigator()

        // Initialize SharedNavigationViewModel observation
        observeNavigationState()

        observeBatteryStatus()

        Log.d(TAG, "NAVIGATION_CHAIN_DEBUG: Fragment view creation completed")
        Log.d(TAG, "NAVIGATION_CHAIN_DEBUG: ═══════════════════════════════════════════════════════════")
    }

    override fun onResume() {
        super.onResume()
        val currentTime = System.currentTimeMillis()
        lastBackNavigationTime = currentTime

        Log.d(TAG, "NAVIGATION_INVESTIGATION: === FRAGMENT RESUME ===")
        Log.d(TAG, "NAVIGATION_INVESTIGATION: Fragment resumed at ${currentTime}")
        Log.d(TAG, "NAVIGATION_INVESTIGATION: Time since creation: ${currentTime - fragmentCreationTime}ms")
        Log.d(TAG, "NAVIGATION_INVESTIGATION: Last navigation source: $lastNavigationSource")
        Log.d(TAG, "NAVIGATION_INVESTIGATION: Click statistics - Total: $totalClickAttempts, Success: $successfulNavigations, Failed: $failedNavigations")
        Log.d(TAG, "NAVIGATION_INVESTIGATION: Fragment state - isAdded: $isAdded, isVisible: $isVisible, isDetached: $isDetached")

        // BLANK_NAVIGATION_DEBUG: Comprehensive resume state logging
        Log.d("OthersFragment_BlankNavigation", "FRAGMENT_RESUME: ═══════════════════════════════════════")
        Log.d("OthersFragment_BlankNavigation", "FRAGMENT_RESUME: Fragment resumed at: $currentTime")
        Log.d("OthersFragment_BlankNavigation", "FRAGMENT_RESUME: Time since creation: ${currentTime - fragmentCreationTime}ms")
        Log.d("OthersFragment_BlankNavigation", "FRAGMENT_RESUME: Fragment state - isAdded: $isAdded, isVisible: $isVisible, isDetached: $isDetached")
        Log.d("OthersFragment_BlankNavigation", "FRAGMENT_RESUME: Fragment lifecycle: ${lifecycle.currentState}")
        Log.d("OthersFragment_BlankNavigation", "FRAGMENT_RESUME: Adapter initialized: ${::othersAdapter.isInitialized}")
        Log.d("OthersFragment_BlankNavigation", "FRAGMENT_RESUME: Current adapter items: ${if (::othersAdapter.isInitialized) othersAdapter.itemCount else 0}")
        Log.d("OthersFragment_BlankNavigation", "FRAGMENT_RESUME: RecyclerView adapter attached: ${binding.othersRecyclerView.adapter != null}")
        Log.d("OthersFragment_BlankNavigation", "FRAGMENT_RESUME: Battery observation active: $isBatteryObservationActive")
        Log.d("OthersFragment_BlankNavigation", "FRAGMENT_RESUME: Current charging state: $isDeviceCharging")
        Log.d("OthersFragment_BlankNavigation", "FRAGMENT_RESUME: Navigation statistics - Total: $totalClickAttempts, Success: $successfulNavigations, Failed: $failedNavigations")

        // Detect if we're resuming from a back navigation
        if (totalClickAttempts > 0) {
            Log.d(TAG, "NAVIGATION_INVESTIGATION: 🔄 RESUMING AFTER NAVIGATION - This could be from back navigation")
            Log.d(TAG, "NAVIGATION_INVESTIGATION: Previous navigation success rate: ${if (totalClickAttempts > 0) (successfulNavigations * 100 / totalClickAttempts) else 0}%")
            Log.d("OthersFragment_BlankNavigation", "FRAGMENT_RESUME: *** BACK NAVIGATION DETECTED ***")
            Log.d("OthersFragment_BlankNavigation", "FRAGMENT_RESUME: Previous navigation success rate: ${if (totalClickAttempts > 0) (successfulNavigations * 100 / totalClickAttempts) else 0}%")
        } else {
            Log.d("OthersFragment_BlankNavigation", "FRAGMENT_RESUME: First resume or no previous navigation")
        }

        Log.d(TAG, "OTHERS_LIFECYCLE: Fragment resumed, refreshing UI state")

        // Refresh anti-theft toggle state in case it was changed in settings
        try {
            binding.switchEnableAntiThief.isChecked = appViewModel.isAntiThiefEnabled()
            Log.d(TAG, "OTHERS_ANTITHEFT: Refreshed toggle state on resume - ${binding.switchEnableAntiThief.isChecked}")
        } catch (e: Exception) {
            Log.w(TAG, "OTHERS_LIFECYCLE: Binding not available during resume", e)
        }

        // CRITICAL_FIX: Validate and synchronize battery state immediately on resume
        Log.d("OthersFragment_ButtonFlicker", "RESUME_STATE_SYNC: Performing battery state validation on resume")
        val stateWasSynchronized = validateAndSynchronizeBatteryState()
        if (stateWasSynchronized) {
            Log.w("OthersFragment_ButtonFlicker", "RESUME_STATE_SYNC: *** STATE WAS OUT OF SYNC ON RESUME ***")
            Log.w("OthersFragment_ButtonFlicker", "RESUME_STATE_SYNC: This could have caused button text flicker!")
        }

        // BACK_NAVIGATION_BLANK_FIX: Check if UI refresh is needed on resume
        // This is crucial for fixing the blank Others Fragment issue after back navigation
        Log.d(TAG, "BACK_NAVIGATION_BLANK_FIX: Checking if UI refresh is needed on resume")
        Log.d("OthersFragment_BlankNavigation", "BLANK_FIX_CHECK: Starting UI refresh check")

        // Check if adapter is initialized and has items
        if (::othersAdapter.isInitialized) {
            val currentItemCount = othersAdapter.itemCount
            Log.d(TAG, "BACK_NAVIGATION_BLANK_FIX: Current adapter item count: $currentItemCount")
            Log.d("OthersFragment_BlankNavigation", "BLANK_FIX_CHECK: Adapter initialized with $currentItemCount items")

            if (currentItemCount == 0) {
                Log.w(TAG, "BACK_NAVIGATION_BLANK_FIX: Adapter is empty, forcing UI refresh")
                Log.w("OthersFragment_BlankNavigation", "BLANK_FIX_CHECK: *** ADAPTER IS EMPTY *** - This could be the blank fragment issue!")
                Log.d("OthersFragment_BlankNavigation", "BLANK_FIX_CHECK: Forcing UI refresh to fix blank fragment")
                refreshAdapterAndListeners()
                Log.d("OthersFragment_BlankNavigation", "BLANK_FIX_CHECK: Post-refresh adapter items: ${if (::othersAdapter.isInitialized) othersAdapter.itemCount else 0}")
            } else {
                Log.d(TAG, "BACK_NAVIGATION_BLANK_FIX: Adapter has items, will refresh anyway for consistency")
                Log.d("OthersFragment_BlankNavigation", "BLANK_FIX_CHECK: Adapter has items but refreshing for consistency")
                // CLICK_LISTENER_FIX: Ensure adapter and click listeners are properly restored
                refreshAdapterAndListeners()
                Log.d("OthersFragment_BlankNavigation", "BLANK_FIX_CHECK: Post-consistency-refresh adapter items: ${if (::othersAdapter.isInitialized) othersAdapter.itemCount else 0}")
            }
        } else {
            Log.w(TAG, "BACK_NAVIGATION_BLANK_FIX: Adapter not initialized, forcing full refresh")
            Log.w("OthersFragment_BlankNavigation", "BLANK_FIX_CHECK: *** ADAPTER NOT INITIALIZED *** - This could be the blank fragment issue!")
            Log.d("OthersFragment_BlankNavigation", "BLANK_FIX_CHECK: Forcing full adapter refresh")
            refreshAdapterAndListeners()
            Log.d("OthersFragment_BlankNavigation", "BLANK_FIX_CHECK: Post-full-refresh adapter items: ${if (::othersAdapter.isInitialized) othersAdapter.itemCount else 0}")
        }

        // BACK_NAVIGATION_BLANK_FIX: Ensure battery observation is active
        // This is crucial because battery observation might have been stopped when fragment was hidden
        Log.d("OthersFragment_BlankNavigation", "BLANK_FIX_CHECK: Checking battery observation state")
        if (!isBatteryObservationActive) {
            Log.w(TAG, "BACK_NAVIGATION_BLANK_FIX: Battery observation not active, restarting")
            Log.w("OthersFragment_BlankNavigation", "BLANK_FIX_CHECK: *** BATTERY OBSERVATION INACTIVE *** - Restarting")
            observeBatteryStatus()
            Log.d("OthersFragment_BlankNavigation", "BLANK_FIX_CHECK: Battery observation restarted")
        } else {
            Log.d(TAG, "BACK_NAVIGATION_BLANK_FIX: Battery observation is active")
            Log.d("OthersFragment_BlankNavigation", "BLANK_FIX_CHECK: Battery observation is active")
        }

        // BLANK_NAVIGATION_FIX: Clear any stale navigation protection state
        if (isNavigationInProgress) {
            val staleDuration = currentTime - navigationProtectionStartTime
            Log.w(TAG, "BLANK_NAVIGATION_FIX: Clearing stale navigation protection (${staleDuration}ms old)")
            Log.w("OthersFragment_BlankNavigation", "STALE_PROTECTION: Clearing stale navigation protection")
            isNavigationInProgress = false
        }

        // NAVIGATION_STATE_FIX: Trigger state recovery if this is a back navigation
        Log.d("OthersFragment_BlankNavigation", "BLANK_FIX_CHECK: Checking if state recovery is needed")
        if (totalClickAttempts > 0) {
            Log.d(TAG, "NAVIGATION_STATE_FIX: Triggering state recovery after back navigation")
            Log.d("OthersFragment_BlankNavigation", "BLANK_FIX_CHECK: Triggering state recovery for back navigation")
            try {
                dynamicNavigationManager.handleBackNavigationStateRecovery()
                Log.d("OthersFragment_BlankNavigation", "BLANK_FIX_CHECK: State recovery completed successfully")
            } catch (e: Exception) {
                Log.e(TAG, "NAVIGATION_STATE_FIX: Error during state recovery", e)
                Log.e("OthersFragment_BlankNavigation", "BLANK_FIX_CHECK: Error during state recovery", e)
            }
        } else {
            Log.d("OthersFragment_BlankNavigation", "BLANK_FIX_CHECK: No previous navigation, skipping state recovery")
        }

        Log.d(TAG, "NAVIGATION_INVESTIGATION: === FRAGMENT RESUME COMPLETED ===")
        Log.d("OthersFragment_BlankNavigation", "FRAGMENT_RESUME: Final state after resume:")
        Log.d("OthersFragment_BlankNavigation", "FRAGMENT_RESUME: - Adapter initialized: ${::othersAdapter.isInitialized}")
        Log.d("OthersFragment_BlankNavigation", "FRAGMENT_RESUME: - Adapter items: ${if (::othersAdapter.isInitialized) othersAdapter.itemCount else 0}")
        Log.d("OthersFragment_BlankNavigation", "FRAGMENT_RESUME: - Battery observation active: $isBatteryObservationActive")
        Log.d("OthersFragment_BlankNavigation", "FRAGMENT_RESUME: - Current charging state: $isDeviceCharging")
        Log.d("OthersFragment_BlankNavigation", "FRAGMENT_RESUME: - RecyclerView adapter attached: ${binding.othersRecyclerView.adapter != null}")
        Log.d("OthersFragment_BlankNavigation", "FRAGMENT_RESUME: ═══════════════════════════════════════")
    }



    override fun onPause() {
        super.onPause()
        Log.d(TAG, "OTHERS_LIFECYCLE: Fragment paused")

        // BUTTON_FLICKERING_FIX: Cancel any pending UI updates when fragment is paused
        pendingUIUpdateJob?.cancel()
        Log.d(TAG, "BUTTON_FLICKERING_FIX: Cancelled pending UI updates on pause")
    }

    override fun onDestroyView() {
        Log.d(TAG, "OTHERS_LIFECYCLE: Fragment view being destroyed")

        // BATTERY_OBSERVATION_FIX: Reset observation state when view is destroyed
        isBatteryObservationActive = false
        pendingUIUpdateJob?.cancel()
        Log.d(TAG, "BATTERY_OBSERVATION_FIX: Reset battery observation state on view destroy")

        super.onDestroyView()
    }

    override fun setupData() {
        super.setupData()
        Log.d(TAG, "OTHERS_FRAGMENT: setupData - configuring RecyclerView and adapter")

        setupRecyclerView()
        updateAdapterItems()
    }

    override fun setupListener() {
        super.setupListener()
        Log.d(TAG, "OTHERS_FRAGMENT: setupListener - configuring anti-theft toggle")

        setupAntiTheftToggle()
    }

    /**
     * Sets up the RecyclerView with the OthersAdapter.
     * Configures linear layout manager and item click handling.
     */
    private fun setupRecyclerView() {
        Log.d(TAG, "OTHERS_RECYCLERVIEW: Setting up RecyclerView with LinearLayoutManager")

        // CLICK_LISTENER_FIX: Only create adapter if not already created
        if (!::othersAdapter.isInitialized) {
            othersAdapter = OthersAdapter { item ->
                handleItemClick(item)
            }
            Log.d(TAG, "OTHERS_RECYCLERVIEW: Created new adapter instance")
        } else {
            Log.d(TAG, "OTHERS_RECYCLERVIEW: Reusing existing adapter instance")
        }

        binding.othersRecyclerView.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = othersAdapter
        }

        Log.d(TAG, "OTHERS_RECYCLERVIEW: RecyclerView setup complete")
    }

    /**
     * CLICK_LISTENER_FIX: Refreshes adapter and ensures click listeners are working.
     * Called on fragment resume to restore functionality after back navigation.
     */
    private fun refreshAdapterAndListeners() {
        Log.d(TAG, "OTHERS_CLICK_FIX: Refreshing adapter and click listeners")

        try {
            // Ensure adapter is properly attached
            if (::othersAdapter.isInitialized && binding.othersRecyclerView.adapter == null) {
                Log.w(TAG, "OTHERS_CLICK_FIX: Adapter was detached, reattaching")
                binding.othersRecyclerView.adapter = othersAdapter
            }

            // Force adapter to refresh its items and click listeners
            if (::othersAdapter.isInitialized) {
                val currentItems = othersAdapter.currentList
                Log.d(TAG, "OTHERS_CLICK_FIX: Refreshing ${currentItems.size} adapter items")
                othersAdapter.notifyDataSetChanged()
            }

            // Update adapter items to ensure they reflect current battery state
            updateAdapterItems()

            Log.d(TAG, "OTHERS_CLICK_FIX: Adapter and click listeners refreshed successfully")
        } catch (e: Exception) {
            Log.e(TAG, "OTHERS_CLICK_FIX: Error refreshing adapter and listeners", e)
            // Fallback: recreate the adapter
            try {
                setupRecyclerView()
                updateAdapterItems()
                Log.d(TAG, "OTHERS_CLICK_FIX: Fallback adapter recreation completed")
            } catch (fallbackException: Exception) {
                Log.e(TAG, "OTHERS_CLICK_FIX: Fallback adapter recreation also failed", fallbackException)
            }
        }
    }

    /**
     * Updates the adapter items based on current battery charging state.
     * Creates appropriate card data for Charge/Discharge, Health, and Settings.
     * ENHANCED_DEBUG_LOGGING: Comprehensive UI update tracking.
     */
    private fun updateAdapterItems() {
        val updateStartTime = System.currentTimeMillis()

        // BLANK_NAVIGATION_FIX: Check navigation protection to prevent corruption
        if (isNavigationInProgress) {
            val timeSinceNavigationStart = updateStartTime - navigationProtectionStartTime
            Log.w(TAG, "BLANK_NAVIGATION_FIX: Skipping UI update during navigation (${timeSinceNavigationStart}ms since start)")
            Log.w("OthersFragment_BlankNavigation", "UI_UPDATE_BLOCKED: Navigation in progress, skipping UI update to prevent corruption")
            return
        }

        // ENHANCED_DEBUG_LOGGING: UI update tracking
        Log.d(TAG, "UI_UPDATE: ═══════════════════════════════════════════════════════════")
        Log.d(TAG, "UI_UPDATE: 🎨 ADAPTER ITEMS UPDATE")
        Log.d(TAG, "UI_UPDATE: ═══════════════════════════════════════════════════════════")
        Log.d(TAG, "UI_UPDATE: Current charging state: $isDeviceCharging")
        Log.d(TAG, "UI_UPDATE: Fragment lifecycle state: isAdded=$isAdded, isVisible=$isVisible")
        Log.d(TAG, "UI_UPDATE: Adapter initialized: ${::othersAdapter.isInitialized}")
        Log.d(TAG, "UI_UPDATE: Update timestamp: $updateStartTime")
        Log.d(TAG, "BLANK_NAVIGATION_FIX: Navigation protection: $isNavigationInProgress")

        // BUTTON_FLICKER_DEBUG: Additional detailed logging
        Log.d("OthersFragment_ButtonFlicker", "ADAPTER_UPDATE: ═══════════════════════════════════════")
        Log.d("OthersFragment_ButtonFlicker", "ADAPTER_UPDATE: Update triggered at: $updateStartTime")
        Log.d("OthersFragment_ButtonFlicker", "ADAPTER_UPDATE: Fragment state - isAdded: $isAdded, isVisible: $isVisible, isDetached: $isDetached")
        Log.d("OthersFragment_ButtonFlicker", "ADAPTER_UPDATE: Fragment lifecycle: ${lifecycle.currentState}")
        Log.d("OthersFragment_ButtonFlicker", "ADAPTER_UPDATE: Current charging state: $isDeviceCharging")
        Log.d("OthersFragment_ButtonFlicker", "ADAPTER_UPDATE: Time since last battery update: ${updateStartTime - lastBatteryStateUpdateTime}ms")
        Log.d("OthersFragment_ButtonFlicker", "ADAPTER_UPDATE: Adapter initialized: ${::othersAdapter.isInitialized}")
        Log.d("OthersFragment_ButtonFlicker", "ADAPTER_UPDATE: Current adapter item count: ${if (::othersAdapter.isInitialized) othersAdapter.itemCount else 0}")
        Log.d("OthersFragment_ButtonFlicker", "ADAPTER_UPDATE: RecyclerView attached: ${binding.othersRecyclerView.adapter != null}")

        val items = listOf(
            createChargeDischargeItem(),
            createHealthItem(),
            createSettingsItem()
        )

        // ENHANCED_DEBUG_LOGGING: Item creation details
        Log.d(TAG, "UI_UPDATE: Created ${items.size} adapter items:")
        items.forEachIndexed { index, item ->
            Log.d(TAG, "UI_UPDATE:   [$index] ${item.title} (ID: ${item.id})")
        }

        // BUTTON_FLICKER_DEBUG: Track item creation details
        Log.d("OthersFragment_ButtonFlicker", "ITEM_CREATION: Created ${items.size} items:")
        items.forEachIndexed { index, item ->
            Log.d("OthersFragment_ButtonFlicker", "ITEM_CREATION:   [$index] Title: '${item.title}', ID: ${item.id}, Icon: ${item.iconResId}")
            if (item.id == CHARGE_DISCHARGE_ITEM_ID) {
                Log.d("OthersFragment_ButtonFlicker", "ITEM_CREATION:   [$index] *** CHARGE/DISCHARGE BUTTON *** - State: $isDeviceCharging")
            }
        }

        Log.d("OthersFragment_ButtonFlicker", "ADAPTER_SUBMIT: Submitting list to adapter...")
        Log.d("OthersFragment_ButtonFlicker", "ADAPTER_SUBMIT: Pre-submit adapter item count: ${if (::othersAdapter.isInitialized) othersAdapter.itemCount else 0}")

        othersAdapter.submitList(items)

        Log.d("OthersFragment_ButtonFlicker", "ADAPTER_SUBMIT: Post-submit adapter item count: ${if (::othersAdapter.isInitialized) othersAdapter.itemCount else 0}")

        val updateDuration = System.currentTimeMillis() - updateStartTime
        Log.d(TAG, "UI_UPDATE: ✅ Adapter update completed in ${updateDuration}ms")
        Log.d("OthersFragment_ButtonFlicker", "ADAPTER_UPDATE: Update completed in ${updateDuration}ms")
        Log.d("OthersFragment_ButtonFlicker", "ADAPTER_UPDATE: Final adapter state - item count: ${if (::othersAdapter.isInitialized) othersAdapter.itemCount else 0}")
        Log.d("OthersFragment_ButtonFlicker", "ADAPTER_UPDATE: ═══════════════════════════════════════")
        Log.d(TAG, "UI_UPDATE: ═══════════════════════════════════════════════════════════")
    }

    /**
     * Creates the charge/discharge item based on current battery status.
     * Dynamically updates title, description, and icon based on charging state.
     * ENHANCED_DEBUG_LOGGING: Button state creation tracking.
     */
    private fun createChargeDischargeItem(): OthersItemData {
        val creationTime = System.currentTimeMillis()

        // NAVIGATION_CHAIN_DEBUG: Enhanced button creation tracking for flicker investigation
        Log.d(TAG, "NAVIGATION_CHAIN_DEBUG: ═══════════════════════════════════════════════════════════")
        Log.d(TAG, "NAVIGATION_CHAIN_DEBUG: 🔘 CHARGE/DISCHARGE BUTTON CREATION")
        Log.d(TAG, "NAVIGATION_CHAIN_DEBUG: ═══════════════════════════════════════════════════════════")
        Log.d(TAG, "NAVIGATION_CHAIN_DEBUG: Creation timestamp: $creationTime")
        Log.d(TAG, "NAVIGATION_CHAIN_DEBUG: Fragment cached charging state: $isDeviceCharging")
        Log.d(TAG, "NAVIGATION_CHAIN_DEBUG: Last battery state update: $lastBatteryStateUpdateTime")
        Log.d(TAG, "NAVIGATION_CHAIN_DEBUG: Time since last battery update: ${creationTime - lastBatteryStateUpdateTime}ms")
        Log.d(TAG, "NAVIGATION_CHAIN_DEBUG: Fragment lifecycle: ${lifecycle.currentState}")
        Log.d(TAG, "NAVIGATION_CHAIN_DEBUG: Fragment visibility: isAdded=$isAdded, isVisible=$isVisible")

        // CRITICAL: Check actual battery state vs cached state for flicker detection
        val currentBatteryStatus = coreBatteryStatsProvider.getCurrentStatus()
        if (currentBatteryStatus != null) {
            val actualChargingState = currentBatteryStatus.isCharging
            Log.d(TAG, "NAVIGATION_CHAIN_DEBUG: Actual battery charging state: $actualChargingState")

            if (isDeviceCharging != actualChargingState) {
                Log.w(TAG, "NAVIGATION_CHAIN_DEBUG: *** CRITICAL FLICKER CONDITION DETECTED ***")
                Log.w(TAG, "NAVIGATION_CHAIN_DEBUG: Cached state: $isDeviceCharging")
                Log.w(TAG, "NAVIGATION_CHAIN_DEBUG: Actual state: $actualChargingState")
                Log.w(TAG, "NAVIGATION_CHAIN_DEBUG: This WILL cause button text flicker!")
                Log.w(TAG, "NAVIGATION_CHAIN_DEBUG: Button will show: ${if (isDeviceCharging) "CHARGE" else "DISCHARGE"}")
                Log.w(TAG, "NAVIGATION_CHAIN_DEBUG: But should show: ${if (actualChargingState) "CHARGE" else "DISCHARGE"}")
            } else {
                Log.d(TAG, "NAVIGATION_CHAIN_DEBUG: Battery state consistent - no flicker risk")
            }
        } else {
            Log.w(TAG, "NAVIGATION_CHAIN_DEBUG: No current battery status available for comparison")
        }

        // ENHANCED_DEBUG_LOGGING: Button state creation tracking
        Log.d(TAG, "BUTTON_STATE: ═══════════════════════════════════════════════════════════")
        Log.d(TAG, "BUTTON_STATE: 🔘 CHARGE/DISCHARGE BUTTON CREATION")
        Log.d(TAG, "BUTTON_STATE: ═══════════════════════════════════════════════════════════")
        Log.d(TAG, "BUTTON_STATE: Battery charging state: $isDeviceCharging")
        Log.d(TAG, "BUTTON_STATE: Creation timestamp: $creationTime")
        Log.d(TAG, "BUTTON_STATE: Time since last update: ${creationTime - lastBatteryStateUpdateTime}ms")

        return if (isDeviceCharging) {
            Log.d(TAG, "BUTTON_STATE: 🔌 Creating CHARGE button (device is charging)")
            Log.d(TAG, "BUTTON_STATE: Button will navigate to: StatsChargeFragment")
            Log.d(TAG, "NAVIGATION_CHAIN_DEBUG: ✅ CREATING CHARGE BUTTON")
            OthersItemData(
                id = CHARGE_DISCHARGE_ITEM_ID,
                title = getString(R.string.charge),
                description = "View charging statistics and power consumption details",
                iconResId = R.drawable.ic_charge_icon
            ).also {
                Log.d(TAG, "BUTTON_STATE: ✅ CHARGE button created successfully")
                Log.d(TAG, "NAVIGATION_CHAIN_DEBUG: CHARGE button creation completed")
            }
        } else {
            Log.d(TAG, "BUTTON_STATE: 🔋 Creating DISCHARGE button (device not charging)")
            Log.d(TAG, "BUTTON_STATE: Button will navigate to: DischargeFragment")
            Log.d(TAG, "NAVIGATION_CHAIN_DEBUG: ✅ CREATING DISCHARGE BUTTON")
            OthersItemData(
                id = CHARGE_DISCHARGE_ITEM_ID,
                title = getString(R.string.discharge),
                description = "View discharge statistics and power consumption details",
                iconResId = R.drawable.ic_discharge_icon
            ).also {
                Log.d(TAG, "BUTTON_STATE: ✅ DISCHARGE button created successfully")
                Log.d(TAG, "NAVIGATION_CHAIN_DEBUG: DISCHARGE button creation completed")
            }
        }.also {
            Log.d(TAG, "BUTTON_STATE: ═══════════════════════════════════════════════════════════")
            Log.d(TAG, "NAVIGATION_CHAIN_DEBUG: Button creation process completed")
            Log.d(TAG, "NAVIGATION_CHAIN_DEBUG: ═══════════════════════════════════════════════════════════")
        }
    }

    /**
     * Creates the health item for battery health navigation.
     */
    private fun createHealthItem(): OthersItemData {
        Log.d(TAG, "OTHERS_ITEMS: Creating health item")
        return OthersItemData(
            id = HEALTH_ITEM_ID,
            title = getString(R.string.health),
            description = "View battery health stats including capacity, voltage, and temperature",
            iconResId = R.drawable.ic_health_icon
        )
    }

    /**
     * Creates the settings item for app settings navigation.
     */
    private fun createSettingsItem(): OthersItemData {
        Log.d(TAG, "OTHERS_ITEMS: Creating settings item")
        return OthersItemData(
            id = SETTINGS_ITEM_ID,
            title = getString(R.string.settings),
            description = "Customize app preferences and behavior to fit your needs",
            iconResId = R.drawable.ic_settings_icon
        )
    }

    /**
     * Handles click events for card items.
     * Routes to appropriate fragments based on item ID and current battery state.
     */
    private fun handleItemClick(item: OthersItemData) {
        totalClickAttempts++
        val currentTime = System.currentTimeMillis()
        val timeSinceCreation = currentTime - fragmentCreationTime
        val timeSinceLastBackNav = currentTime - lastBackNavigationTime

        Log.d(TAG, "NAVIGATION_INVESTIGATION: === CLICK ATTEMPT #$totalClickAttempts ===")
        Log.d(TAG, "NAVIGATION_INVESTIGATION: Item clicked: ${item.id}")
        Log.d(TAG, "NAVIGATION_INVESTIGATION: Fragment state - isAdded: $isAdded, isVisible: $isVisible, isDetached: $isDetached, isRemoving: $isRemoving")
        Log.d(TAG, "NAVIGATION_INVESTIGATION: Activity state - activity: ${activity != null}, context: ${context != null}")
        Log.d(TAG, "NAVIGATION_INVESTIGATION: Fragment lifecycle - created ${timeSinceCreation}ms ago, last back nav ${timeSinceLastBackNav}ms ago")
        Log.d(TAG, "NAVIGATION_INVESTIGATION: Last navigation source: $lastNavigationSource")
        Log.d(TAG, "NAVIGATION_INVESTIGATION: Adapter state - initialized: ${::othersAdapter.isInitialized}")
        if (::othersAdapter.isInitialized) {
            Log.d(TAG, "NAVIGATION_INVESTIGATION: Adapter items count: ${othersAdapter.currentList.size}")
            Log.d(TAG, "NAVIGATION_INVESTIGATION: RecyclerView adapter attached: ${binding.othersRecyclerView.adapter != null}")
        }
        Log.d(TAG, "NAVIGATION_INVESTIGATION: Battery state - charging: $isDeviceCharging")
        Log.d(TAG, "NAVIGATION_INVESTIGATION: Success rate: $successfulNavigations/$totalClickAttempts (${if (totalClickAttempts > 0) (successfulNavigations * 100 / totalClickAttempts) else 0}%)")

        // CLICK_LISTENER_FIX: Add defensive checks to ensure fragment is in proper state
        if (!isAdded || isDetached || activity == null) {
            failedNavigations++
            Log.w(TAG, "NAVIGATION_INVESTIGATION: ❌ NAVIGATION FAILED - Fragment not in proper state")
            Log.w(TAG, "NAVIGATION_INVESTIGATION: Failed navigation #$failedNavigations - isAdded: $isAdded, isDetached: $isDetached, activity: ${activity != null}")
            return
        }

        // CLICK_LISTENER_FIX: Add debouncing to prevent rapid clicks
        if (currentTime - lastClickTime < CLICK_DEBOUNCE_DELAY) {
            Log.d(TAG, "NAVIGATION_INVESTIGATION: Click ignored due to debouncing (${currentTime - lastClickTime}ms < ${CLICK_DEBOUNCE_DELAY}ms)")
            return
        }
        lastClickTime = currentTime

        when (item.id) {
            CHARGE_DISCHARGE_ITEM_ID -> {
                Log.d(TAG, "NAVIGATION_INVESTIGATION: Processing charge/discharge navigation using AppNavigator...")
                val navigationResult = handleChargeDischargeNavigationWithAppNavigator()
                if (navigationResult) {
                    successfulNavigations++
                    Log.d(TAG, "NAVIGATION_INVESTIGATION: ✅ NAVIGATION SUCCESSFUL #$successfulNavigations")
                } else {
                    failedNavigations++
                    Log.e(TAG, "NAVIGATION_INVESTIGATION: ❌ NAVIGATION FAILED #$failedNavigations")
                }
            }
            HEALTH_ITEM_ID -> {
                Log.d(TAG, "OTHERS_NAVIGATION: Navigating to HealthFragment using AppNavigator")
                val navigationResult = appNavigator.navigateToHealth()
                if (navigationResult) {
                    successfulNavigations++
                    Log.d(TAG, "OTHERS_NAVIGATION: ✅ Health navigation successful")
                } else {
                    failedNavigations++
                    Log.e(TAG, "OTHERS_NAVIGATION: ❌ Health navigation failed")
                }
            }
            SETTINGS_ITEM_ID -> {
                Log.d(TAG, "OTHERS_NAVIGATION: Navigating to SettingsFragment using AppNavigator")
                val navigationResult = appNavigator.navigateToSettings()
                if (navigationResult) {
                    successfulNavigations++
                    Log.d(TAG, "OTHERS_NAVIGATION: ✅ Settings navigation successful")
                } else {
                    failedNavigations++
                    Log.e(TAG, "OTHERS_NAVIGATION: ❌ Settings navigation failed")
                }
            }
            else -> {
                failedNavigations++
                Log.w(TAG, "NAVIGATION_INVESTIGATION: ❌ Unknown item clicked - ${item.id}")
            }
        }

        Log.d(TAG, "NAVIGATION_INVESTIGATION: === CLICK ATTEMPT #$totalClickAttempts COMPLETED ===")
    }

    /**
     * Handles charge/discharge navigation using the centralized AppNavigator.
     * Falls back to legacy DynamicNavigationManager if AppNavigator fails.
     * CRITICAL_FIX: Added battery state validation to prevent blank navigation.
     * @return true if navigation was successful, false otherwise
     */
    private fun handleChargeDischargeNavigationWithAppNavigator(): Boolean {
        val navigationStartTime = System.currentTimeMillis()

        // NAVIGATION_CHAIN_TEST: Unified logging for complete navigation chain tracking
        Log.d("NAVIGATION_CHAIN_TEST", "═══════════════════════════════════════════════════════════")
        Log.d("NAVIGATION_CHAIN_TEST", "🚀 OTHERS FRAGMENT: CHARGE/DISCHARGE NAVIGATION START")
        Log.d("NAVIGATION_CHAIN_TEST", "═══════════════════════════════════════════════════════════")
        Log.d("NAVIGATION_CHAIN_TEST", "Navigation timestamp: $navigationStartTime")
        Log.d("NAVIGATION_CHAIN_TEST", "Fragment state: isAdded=$isAdded, isVisible=$isVisible")
        Log.d("NAVIGATION_CHAIN_TEST", "Fragment lifecycle: ${lifecycle.currentState}")
        Log.d("NAVIGATION_CHAIN_TEST", "Battery charging state: $isDeviceCharging")
        Log.d("NAVIGATION_CHAIN_TEST", "Navigation statistics: Total=$totalClickAttempts, Success=$successfulNavigations, Failed=$failedNavigations")
        Log.d("NAVIGATION_CHAIN_TEST", "Last navigation source: $lastNavigationSource")

        // BLANK_NAVIGATION_FIX: Set navigation protection to prevent UI corruption
        if (isNavigationInProgress) {
            Log.w(TAG, "BLANK_NAVIGATION_FIX: Navigation already in progress, preventing concurrent navigation")
            Log.w("OthersFragment_BlankNavigation", "NAVIGATION_PROTECTION: Concurrent navigation blocked")
            Log.w("NAVIGATION_CHAIN_TEST", "❌ NAVIGATION BLOCKED: Concurrent navigation attempt")
            return false
        }

        isNavigationInProgress = true
        navigationProtectionStartTime = navigationStartTime

        Log.d(TAG, "NAVIGATION_INVESTIGATION: === APPNAVIGATOR CHARGE/DISCHARGE NAVIGATION START ===")
        Log.d(TAG, "NAVIGATION_INVESTIGATION: Current battery charging state: $isDeviceCharging")
        Log.d(TAG, "NAVIGATION_INVESTIGATION: AppNavigator initialized: ${appNavigator.isInitialized()}")
        Log.d(TAG, "BLANK_NAVIGATION_FIX: Navigation protection enabled at: $navigationProtectionStartTime")
        Log.d("NAVIGATION_CHAIN_TEST", "🔒 NAVIGATION PROTECTION: Enabled at $navigationProtectionStartTime")

        // CRITICAL_FIX: Validate battery state before navigation to prevent blank navigation
        Log.d("OthersFragment_BlankNavigation", "NAV_STATE_VALIDATION: ═══════════════════════════════════════")
        Log.d("OthersFragment_BlankNavigation", "NAV_STATE_VALIDATION: Pre-navigation state validation")
        Log.d("OthersFragment_BlankNavigation", "NAV_STATE_VALIDATION: Fragment charging state: $isDeviceCharging")
        Log.d("OthersFragment_BlankNavigation", "NAV_STATE_VALIDATION: Navigation protection active: $isNavigationInProgress")
        Log.d("NAVIGATION_CHAIN_TEST", "🔍 STATE VALIDATION: Fragment charging state = $isDeviceCharging")

        val currentBatteryStatus = coreBatteryStatsProvider.getCurrentStatus()
        if (currentBatteryStatus != null) {
            val actualChargingState = currentBatteryStatus.isCharging
            Log.d("OthersFragment_BlankNavigation", "NAV_STATE_VALIDATION: Actual battery charging state: $actualChargingState")

            if (isDeviceCharging != actualChargingState) {
                Log.w("OthersFragment_BlankNavigation", "NAV_STATE_VALIDATION: *** STATE MISMATCH DETECTED ***")
                Log.w("OthersFragment_BlankNavigation", "NAV_STATE_VALIDATION: Fragment state: $isDeviceCharging, Actual state: $actualChargingState")
                Log.w("OthersFragment_BlankNavigation", "NAV_STATE_VALIDATION: Correcting state before navigation")

                // CRITICAL_FIX: Correct the state immediately but prevent navigation corruption
                val previousState = isDeviceCharging
                isDeviceCharging = actualChargingState
                lastBatteryStateUpdateTime = System.currentTimeMillis()

                Log.d("OthersFragment_BlankNavigation", "NAV_STATE_VALIDATION: State corrected: $previousState → $isDeviceCharging")
                Log.d("OthersFragment_BlankNavigation", "NAV_STATE_VALIDATION: ⚠️  CRITICAL: Skipping UI update to prevent navigation corruption")
                Log.d("OthersFragment_BlankNavigation", "NAV_STATE_VALIDATION: Navigation will proceed with corrected state")

                // CRITICAL_FIX: Do NOT update UI here to prevent navigation corruption
                // The UI will be updated after successful navigation
                // updateAdapterItems() // REMOVED to prevent blank navigation
            } else {
                Log.d("OthersFragment_BlankNavigation", "NAV_STATE_VALIDATION: State consistent, proceeding with navigation")
            }
        } else {
            Log.w("OthersFragment_BlankNavigation", "NAV_STATE_VALIDATION: No current battery status available")
            Log.w("OthersFragment_BlankNavigation", "NAV_STATE_VALIDATION: Using fragment state: $isDeviceCharging")
        }
        Log.d("OthersFragment_BlankNavigation", "NAV_STATE_VALIDATION: Final navigation state: $isDeviceCharging")
        Log.d("OthersFragment_BlankNavigation", "NAV_STATE_VALIDATION: ═══════════════════════════════════════")

        // Try AppNavigator first (modern approach) with validated state
        val navigationResult = if (isDeviceCharging) {
            Log.d(TAG, "NAVIGATION_INVESTIGATION: Device charging - using AppNavigator.navigateToCharge()")
            appNavigator.navigateToCharge()
        } else {
            Log.d(TAG, "NAVIGATION_INVESTIGATION: Device not charging - using AppNavigator.navigateToDischarge()")
            appNavigator.navigateToDischarge()
        }

        // BLANK_NAVIGATION_FIX: Clear navigation protection and return result
        val finalResult = if (navigationResult) {
            Log.d(TAG, "NAVIGATION_INVESTIGATION: ✅ AppNavigator navigation successful")
            Log.d(TAG, "BLANK_NAVIGATION_FIX: Navigation completed successfully")
            Log.d("NAVIGATION_CHAIN_TEST", "✅ NAVIGATION SUCCESS: AppNavigator completed successfully")
            true
        } else {
            Log.w(TAG, "NAVIGATION_INVESTIGATION: ❌ AppNavigator failed, falling back to legacy navigation")
            Log.w(TAG, "BLANK_NAVIGATION_FIX: AppNavigator failed, trying legacy navigation")
            Log.w("NAVIGATION_CHAIN_TEST", "⚠️ NAVIGATION FALLBACK: AppNavigator failed, using legacy")
            handleChargeDischargeNavigationLegacy()
        }

        // BLANK_NAVIGATION_FIX: Clear navigation protection
        isNavigationInProgress = false
        val navigationDuration = System.currentTimeMillis() - navigationProtectionStartTime
        Log.d(TAG, "BLANK_NAVIGATION_FIX: Navigation protection cleared after ${navigationDuration}ms")
        Log.d("OthersFragment_BlankNavigation", "NAVIGATION_PROTECTION: Protection cleared, result: $finalResult")
        Log.d("NAVIGATION_CHAIN_TEST", "🔓 NAVIGATION PROTECTION: Cleared after ${navigationDuration}ms, result: $finalResult")
        Log.d("NAVIGATION_CHAIN_TEST", "═══════════════════════════════════════════════════════════")

        return finalResult
    }

    /**
     * Legacy charge/discharge navigation using DynamicNavigationManager.
     * Kept as fallback for compatibility.
     * CRITICAL_FIX: Added additional state validation for legacy navigation.
     * @return true if navigation was successful, false otherwise
     */
    private fun handleChargeDischargeNavigationLegacy(): Boolean {
        Log.d(TAG, "NAVIGATION_INVESTIGATION: === LEGACY CHARGE/DISCHARGE NAVIGATION START ===")
        Log.d(TAG, "NAVIGATION_INVESTIGATION: Current battery charging state: $isDeviceCharging")
        Log.d(TAG, "NAVIGATION_INVESTIGATION: DynamicNavigationManager initialized: ${dynamicNavigationManager.isInitialized()}")

        // CRITICAL_FIX: Additional state validation for legacy navigation
        Log.d("OthersFragment_BlankNavigation", "LEGACY_NAV_VALIDATION: Performing additional state validation")
        val currentBatteryStatus = coreBatteryStatsProvider.getCurrentStatus()
        if (currentBatteryStatus != null && isDeviceCharging != currentBatteryStatus.isCharging) {
            Log.w("OthersFragment_BlankNavigation", "LEGACY_NAV_VALIDATION: State mismatch in legacy navigation")
            Log.w("OthersFragment_BlankNavigation", "LEGACY_NAV_VALIDATION: Fragment: $isDeviceCharging, Actual: ${currentBatteryStatus.isCharging}")
            isDeviceCharging = currentBatteryStatus.isCharging
            Log.d("OthersFragment_BlankNavigation", "LEGACY_NAV_VALIDATION: State corrected to: $isDeviceCharging")
        }

        var navigationSuccess = false

        // Try DynamicNavigationManager first
        if (dynamicNavigationManager.isInitialized()) {
            val targetFragmentId = if (isDeviceCharging) {
                Log.d(TAG, "NAVIGATION_INVESTIGATION: Device charging - targeting charge fragment (R.id.chargeFragment)")
                R.id.chargeFragment
            } else {
                Log.d(TAG, "NAVIGATION_INVESTIGATION: Device not charging - targeting discharge fragment (R.id.dischargeFragment)")
                R.id.dischargeFragment
            }

            Log.d(TAG, "NAVIGATION_INVESTIGATION: Calling dynamicNavigationManager.handleUserNavigation($targetFragmentId)")
            navigationSuccess = dynamicNavigationManager.handleUserNavigation(targetFragmentId)
            Log.d(TAG, "NAVIGATION_INVESTIGATION: DynamicNavigationManager result: $navigationSuccess")

            if (!navigationSuccess) {
                Log.w(TAG, "NAVIGATION_INVESTIGATION: ❌ DynamicNavigationManager failed, attempting fallback navigation")
                val fallbackResult = performFallbackNavigation(targetFragmentId)
                Log.d(TAG, "NAVIGATION_INVESTIGATION: Fallback navigation result: $fallbackResult")
                navigationSuccess = fallbackResult
            } else {
                Log.d(TAG, "NAVIGATION_INVESTIGATION: ✅ DynamicNavigationManager navigation successful")
            }
        } else {
            Log.w(TAG, "NAVIGATION_INVESTIGATION: ❌ DynamicNavigationManager not initialized, using fallback")
            val targetFragmentId = if (isDeviceCharging) R.id.chargeFragment else R.id.dischargeFragment
            val fallbackResult = performFallbackNavigation(targetFragmentId)
            Log.d(TAG, "NAVIGATION_INVESTIGATION: Fallback navigation result: $fallbackResult")
            navigationSuccess = fallbackResult
        }

        Log.d(TAG, "NAVIGATION_INVESTIGATION: === LEGACY CHARGE/DISCHARGE NAVIGATION ${if (navigationSuccess) "SUCCESS" else "FAILED"} ===")
        return navigationSuccess
    }

    /**
     * Performs fallback navigation when DynamicNavigationManager fails.
     * Uses direct fragment manager navigation.
     * @return true if navigation was successful, false otherwise
     */
    private fun performFallbackNavigation(fragmentId: Int): Boolean {
        Log.d(TAG, "NAVIGATION_INVESTIGATION: === FALLBACK NAVIGATION START ===")
        Log.d(TAG, "NAVIGATION_INVESTIGATION: Target fragment ID: $fragmentId")

        return try {
            val fragment = when (fragmentId) {
                R.id.chargeFragment -> {
                    Log.d(TAG, "NAVIGATION_INVESTIGATION: Creating StatsChargeFragment")
                    com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment()
                }
                R.id.dischargeFragment -> {
                    Log.d(TAG, "NAVIGATION_INVESTIGATION: Creating DischargeFragment")
                    com.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment()
                }
                else -> {
                    Log.e(TAG, "NAVIGATION_INVESTIGATION: ❌ Unknown fragment ID: $fragmentId")
                    return false
                }
            }

            Log.d(TAG, "NAVIGATION_INVESTIGATION: Fragment created successfully: ${fragment.javaClass.simpleName}")
            Log.d(TAG, "NAVIGATION_INVESTIGATION: Using activity fragment manager for navigation")

            // Use activity's fragment manager for proper navigation
            requireActivity().supportFragmentManager
                .beginTransaction()
                .replace(R.id.nav_host_fragment, fragment)
                .addToBackStack(null)
                .commit()

            Log.d(TAG, "NAVIGATION_INVESTIGATION: ✅ Fallback navigation transaction committed successfully")
            Log.d(TAG, "NAVIGATION_INVESTIGATION: === FALLBACK NAVIGATION SUCCESS ===")
            true

        } catch (e: Exception) {
            Log.e(TAG, "NAVIGATION_INVESTIGATION: ❌ Exception in fallback navigation", e)
            Log.e(TAG, "NAVIGATION_INVESTIGATION: Exception type: ${e.javaClass.simpleName}")
            Log.e(TAG, "NAVIGATION_INVESTIGATION: Exception message: ${e.message}")
            Log.d(TAG, "NAVIGATION_INVESTIGATION: === FALLBACK NAVIGATION FAILED ===")
            false
        }
    }

    /**
     * Navigates to the specified fragment using fragment manager.
     * Fallback method for direct fragment navigation.
     */
    private fun navigateToFragment(fragment: androidx.fragment.app.Fragment) {
        Log.d(TAG, "OTHERS_NAVIGATION: Direct fragment navigation to ${fragment.javaClass.simpleName}")

        parentFragmentManager.beginTransaction()
            .replace(R.id.nav_host_fragment, fragment)
            .addToBackStack(null)
            .commit()
    }

    /**
     * Initializes the AppNavigator with required dependencies.
     * This enables centralized navigation management.
     */
    private fun initializeAppNavigator() {
        Log.d(TAG, "APPNAVIGATOR_INIT: Initializing AppNavigator from OthersFragment")

        try {
            val activity = requireActivity()
            val fragmentManager = activity.supportFragmentManager
            val bottomNavigationView = activity.findViewById<com.google.android.material.bottomnavigation.BottomNavigationView>(R.id.bottom_view)
            val fragmentContainerId = R.id.nav_host_fragment

            if (bottomNavigationView != null) {
                appNavigator.initialize(
                    fragmentManager = fragmentManager,
                    bottomNavigationView = bottomNavigationView,
                    fragmentContainerId = fragmentContainerId,
                    lifecycleOwner = this
                )
                Log.d(TAG, "APPNAVIGATOR_INIT: AppNavigator initialized successfully")
            } else {
                Log.e(TAG, "APPNAVIGATOR_INIT: BottomNavigationView not found, AppNavigator initialization failed")
            }
        } catch (e: Exception) {
            Log.e(TAG, "APPNAVIGATOR_INIT: Error initializing AppNavigator", e)
        }
    }

    /**
     * Observes battery status changes from CoreBatteryStatsProvider.
     * Updates UI when charging state changes with debouncing to prevent flickering.
     * BATTERY_OBSERVATION_FIX: Prevents multiple concurrent observations and adds debouncing.
     */
    private fun observeBatteryStatus() {
        // BATTERY_OBSERVATION_FIX: Prevent multiple concurrent observations
        if (isBatteryObservationActive) {
            Log.d(TAG, "OTHERS_BATTERY: Battery observation already active, skipping duplicate setup")
            Log.d("OthersFragment_ButtonFlicker", "OBSERVATION_DUPLICATE: Battery observation already active")
            return
        }

        Log.d(TAG, "OTHERS_BATTERY: Starting battery status observation")
        Log.d("OthersFragment_ButtonFlicker", "OBSERVATION_START: ═══════════════════════════════════════")
        Log.d("OthersFragment_ButtonFlicker", "OBSERVATION_START: Fragment state - isAdded: $isAdded, isVisible: $isVisible, isDetached: $isDetached")
        Log.d("OthersFragment_ButtonFlicker", "OBSERVATION_START: Current charging state: $isDeviceCharging")
        Log.d("OthersFragment_ButtonFlicker", "OBSERVATION_START: Fragment lifecycle: ${lifecycle.currentState}")
        Log.d("OthersFragment_ButtonFlicker", "OBSERVATION_START: Adapter initialized: ${::othersAdapter.isInitialized}")
        Log.d("OthersFragment_ButtonFlicker", "OBSERVATION_START: Last update time: $lastBatteryStateUpdateTime")
        isBatteryObservationActive = true

        lifecycleScope.launch {
            try {
                Log.d(TAG, "OTHERS_BATTERY: Collecting from coreBatteryStatusFlow")
                Log.d("OthersFragment_ButtonFlicker", "FLOW_COLLECTION: Starting battery status flow collection")
                Log.d("OthersFragment_ButtonFlicker", "FLOW_COLLECTION: Lifecycle scope state: ${lifecycle.currentState}")

                coreBatteryStatsProvider.coreBatteryStatusFlow
                    .filterNotNull()
                    .collect { status ->
                        val currentTime = System.currentTimeMillis()
                        Log.d(TAG, "OTHERS_BATTERY: Received battery status - charging: ${status.isCharging}, percentage: ${status.percentage}")

                        Log.d("OthersFragment_ButtonFlicker", "STATUS_RECEIVED: ═══════════════════════════════════════")
                        Log.d("OthersFragment_ButtonFlicker", "STATUS_RECEIVED: Timestamp: $currentTime")
                        Log.d("OthersFragment_ButtonFlicker", "STATUS_RECEIVED: Battery percentage: ${status.percentage}%")
                        Log.d("OthersFragment_ButtonFlicker", "STATUS_RECEIVED: Current charging state: $isDeviceCharging")
                        Log.d("OthersFragment_ButtonFlicker", "STATUS_RECEIVED: New charging state: ${status.isCharging}")
                        Log.d("OthersFragment_ButtonFlicker", "STATUS_RECEIVED: Fragment lifecycle: ${lifecycle.currentState}")
                        Log.d("OthersFragment_ButtonFlicker", "STATUS_RECEIVED: Fragment visibility: isAdded=$isAdded, isVisible=$isVisible")
                        Log.d("OthersFragment_ButtonFlicker", "STATUS_RECEIVED: Last update: $lastBatteryStateUpdateTime (${currentTime - lastBatteryStateUpdateTime}ms ago)")
                        Log.d("OthersFragment_ButtonFlicker", "STATUS_RECEIVED: Adapter initialized: ${::othersAdapter.isInitialized}")
                        Log.d("OthersFragment_ButtonFlicker", "STATUS_RECEIVED: Current adapter items: ${if (::othersAdapter.isInitialized) othersAdapter.itemCount else 0}")

                        val wasCharging = isDeviceCharging
                        isDeviceCharging = status.isCharging

                        if (wasCharging != isDeviceCharging) {
                            Log.d(TAG, "OTHERS_BATTERY: Charging state changed from $wasCharging to $isDeviceCharging")
                            Log.d("OthersFragment_ButtonFlicker", "STATE_CHANGE: ═══════════════════════════════════════")
                            Log.d("OthersFragment_ButtonFlicker", "STATE_CHANGE: Previous state: $wasCharging")
                            Log.d("OthersFragment_ButtonFlicker", "STATE_CHANGE: New state: $isDeviceCharging")
                            Log.d("OthersFragment_ButtonFlicker", "STATE_CHANGE: Change timestamp: $currentTime")
                            Log.d("OthersFragment_ButtonFlicker", "STATE_CHANGE: Fragment valid: isAdded=$isAdded, isVisible=$isVisible")

                            // BUTTON_FLICKERING_FIX: Debounce UI updates to prevent rapid changes
                            val timeSinceLastUpdate = currentTime - lastBatteryStateUpdateTime
                            Log.d("OthersFragment_ButtonFlicker", "DEBOUNCE_CHECK: Time since last update: ${timeSinceLastUpdate}ms")
                            Log.d("OthersFragment_ButtonFlicker", "DEBOUNCE_CHECK: Debounce threshold: 500ms")
                            Log.d("OthersFragment_ButtonFlicker", "DEBOUNCE_CHECK: Pending job exists: ${pendingUIUpdateJob != null}")

                            if (timeSinceLastUpdate < 500) { // 500ms debounce
                                Log.d(TAG, "BUTTON_FLICKERING_FIX: Debouncing UI update (${timeSinceLastUpdate}ms since last)")
                                Log.d("OthersFragment_ButtonFlicker", "DEBOUNCED_UPDATE: Scheduling debounced update")
                                scheduleDebounceUIUpdate()
                            } else {
                                Log.d(TAG, "BUTTON_FLICKERING_FIX: Immediate UI update (${timeSinceLastUpdate}ms since last)")
                                Log.d("OthersFragment_ButtonFlicker", "IMMEDIATE_UPDATE: Executing immediate update")
                                Log.d("OthersFragment_ButtonFlicker", "IMMEDIATE_UPDATE: Pre-update adapter items: ${if (::othersAdapter.isInitialized) othersAdapter.itemCount else 0}")
                                lastBatteryStateUpdateTime = currentTime
                                updateAdapterItems()
                                Log.d("OthersFragment_ButtonFlicker", "IMMEDIATE_UPDATE: Post-update adapter items: ${if (::othersAdapter.isInitialized) othersAdapter.itemCount else 0}")
                                Log.d("OthersFragment_ButtonFlicker", "IMMEDIATE_UPDATE: Update completed at: $lastBatteryStateUpdateTime")
                            }
                        } else {
                            Log.v(TAG, "OTHERS_BATTERY: Charging state unchanged: $isDeviceCharging")
                            Log.d("OthersFragment_ButtonFlicker", "NO_STATE_CHANGE: Charging state unchanged ($isDeviceCharging), no UI update needed")
                        }
                        Log.d("OthersFragment_ButtonFlicker", "STATUS_PROCESSED: ═══════════════════════════════════════")
                    }
            } catch (e: Exception) {
                Log.e(TAG, "OTHERS_BATTERY: Error observing battery status", e)
                Log.e("OthersFragment_ButtonFlicker", "OBSERVATION_ERROR: Exception in battery observation", e)
                Log.d("OthersFragment_ButtonFlicker", "OBSERVATION_ERROR: Resetting observation state and attempting fallback")
                isBatteryObservationActive = false // Reset flag on error

                // Fallback to default state and try to get current status
                try {
                    val currentStatus = coreBatteryStatsProvider.getCurrentStatus()
                    if (currentStatus != null) {
                        Log.d(TAG, "OTHERS_BATTERY: Using current status as fallback - charging: ${currentStatus.isCharging}")
                        Log.d("OthersFragment_ButtonFlicker", "FALLBACK_SUCCESS: Using current status - charging: ${currentStatus.isCharging}")
                        isDeviceCharging = currentStatus.isCharging
                    } else {
                        Log.w(TAG, "OTHERS_BATTERY: No current status available, defaulting to not charging")
                        Log.w("OthersFragment_ButtonFlicker", "FALLBACK_NULL: No current status available, defaulting to false")
                        isDeviceCharging = false
                    }
                } catch (fallbackException: Exception) {
                    Log.e(TAG, "OTHERS_BATTERY: Error getting current status", fallbackException)
                    Log.e("OthersFragment_ButtonFlicker", "FALLBACK_ERROR: Error getting current status", fallbackException)
                    isDeviceCharging = false
                }
                Log.d("OthersFragment_ButtonFlicker", "FALLBACK_UPDATE: Updating adapter with fallback state: $isDeviceCharging")
                updateAdapterItems()
            }
        }
    }

    /**
     * CRITICAL_FIX: Validates and synchronizes battery state with CoreBatteryStatsProvider.
     * This method ensures the fragment's charging state matches the actual battery state
     * to prevent button text flicker and blank navigation issues.
     *
     * @return true if state was synchronized, false if no change was needed
     */
    private fun validateAndSynchronizeBatteryState(): Boolean {
        Log.d("OthersFragment_ButtonFlicker", "STATE_SYNC: ═══════════════════════════════════════")
        Log.d("OthersFragment_ButtonFlicker", "STATE_SYNC: Validating battery state synchronization")
        Log.d("OthersFragment_ButtonFlicker", "STATE_SYNC: Current fragment state: $isDeviceCharging")

        val currentBatteryStatus = coreBatteryStatsProvider.getCurrentStatus()
        if (currentBatteryStatus != null) {
            val actualChargingState = currentBatteryStatus.isCharging
            Log.d("OthersFragment_ButtonFlicker", "STATE_SYNC: Actual battery state: $actualChargingState")

            if (isDeviceCharging != actualChargingState) {
                Log.w("OthersFragment_ButtonFlicker", "STATE_SYNC: *** STATE MISMATCH DETECTED ***")
                Log.w("OthersFragment_ButtonFlicker", "STATE_SYNC: Fragment: $isDeviceCharging → Actual: $actualChargingState")

                val previousState = isDeviceCharging
                isDeviceCharging = actualChargingState
                lastBatteryStateUpdateTime = System.currentTimeMillis()

                Log.d("OthersFragment_ButtonFlicker", "STATE_SYNC: State synchronized: $previousState → $isDeviceCharging")
                Log.d("OthersFragment_ButtonFlicker", "STATE_SYNC: Update timestamp: $lastBatteryStateUpdateTime")
                Log.d("OthersFragment_ButtonFlicker", "STATE_SYNC: ═══════════════════════════════════════")
                return true
            } else {
                Log.d("OthersFragment_ButtonFlicker", "STATE_SYNC: State already synchronized")
                Log.d("OthersFragment_ButtonFlicker", "STATE_SYNC: ═══════════════════════════════════════")
                return false
            }
        } else {
            Log.w("OthersFragment_ButtonFlicker", "STATE_SYNC: No current battery status available")
            Log.w("OthersFragment_ButtonFlicker", "STATE_SYNC: Keeping existing state: $isDeviceCharging")
            Log.d("OthersFragment_ButtonFlicker", "STATE_SYNC: ═══════════════════════════════════════")
            return false
        }
    }

    /**
     * BUTTON_FLICKERING_FIX: Schedules a debounced UI update to prevent rapid button state changes.
     */
    private fun scheduleDebounceUIUpdate() {
        Log.d("OthersFragment_ButtonFlicker", "DEBOUNCE_SCHEDULE: ═══════════════════════════════════════")
        Log.d("OthersFragment_ButtonFlicker", "DEBOUNCE_SCHEDULE: Cancelling existing pending job: ${pendingUIUpdateJob != null}")
        Log.d("OthersFragment_ButtonFlicker", "DEBOUNCE_SCHEDULE: Fragment state - isAdded: $isAdded, isVisible: $isVisible")
        Log.d("OthersFragment_ButtonFlicker", "DEBOUNCE_SCHEDULE: Current charging state: $isDeviceCharging")

        // Cancel any pending update
        pendingUIUpdateJob?.cancel()

        pendingUIUpdateJob = lifecycleScope.launch {
            Log.d("OthersFragment_ButtonFlicker", "DEBOUNCE_DELAY: Starting 300ms delay")
            delay(300) // 300ms debounce delay

            Log.d(TAG, "BUTTON_FLICKERING_FIX: Executing debounced UI update")
            Log.d("OthersFragment_ButtonFlicker", "DEBOUNCE_EXECUTE: ═══════════════════════════════════════")
            Log.d("OthersFragment_ButtonFlicker", "DEBOUNCE_EXECUTE: Fragment state after delay - isAdded: $isAdded, isVisible: $isVisible")
            Log.d("OthersFragment_ButtonFlicker", "DEBOUNCE_EXECUTE: Fragment lifecycle: ${lifecycle.currentState}")
            Log.d("OthersFragment_ButtonFlicker", "DEBOUNCE_EXECUTE: Charging state: $isDeviceCharging")
            Log.d("OthersFragment_ButtonFlicker", "DEBOUNCE_EXECUTE: Adapter initialized: ${::othersAdapter.isInitialized}")
            Log.d("OthersFragment_ButtonFlicker", "DEBOUNCE_EXECUTE: Pre-update adapter items: ${if (::othersAdapter.isInitialized) othersAdapter.itemCount else 0}")

            if (isAdded && isVisible) {
                // BLANK_NAVIGATION_FIX: Check navigation protection before debounced update
                if (isNavigationInProgress) {
                    val timeSinceNavigationStart = System.currentTimeMillis() - navigationProtectionStartTime
                    Log.w("OthersFragment_ButtonFlicker", "DEBOUNCE_EXECUTE: Skipping debounced update during navigation (${timeSinceNavigationStart}ms)")
                    Log.w("OthersFragment_BlankNavigation", "DEBOUNCED_UPDATE_BLOCKED: Navigation in progress, preventing UI corruption")
                } else {
                    lastBatteryStateUpdateTime = System.currentTimeMillis()
                    Log.d("OthersFragment_ButtonFlicker", "DEBOUNCE_EXECUTE: Fragment valid, executing update at: $lastBatteryStateUpdateTime")
                    updateAdapterItems()
                    Log.d("OthersFragment_ButtonFlicker", "DEBOUNCE_EXECUTE: Post-update adapter items: ${if (::othersAdapter.isInitialized) othersAdapter.itemCount else 0}")
                    Log.d("OthersFragment_ButtonFlicker", "DEBOUNCE_EXECUTE: Update completed successfully")
                }
            } else {
                Log.w("OthersFragment_ButtonFlicker", "DEBOUNCE_EXECUTE: Fragment invalid, skipping update - isAdded: $isAdded, isVisible: $isVisible")
            }
            Log.d("OthersFragment_ButtonFlicker", "DEBOUNCE_EXECUTE: ═══════════════════════════════════════")
        }
        Log.d("OthersFragment_ButtonFlicker", "DEBOUNCE_SCHEDULE: New debounce job scheduled")
    }

    /**
     * Sets up the anti-theft toggle switch functionality.
     * Handles toggle state changes and password management.
     */
    private fun setupAntiTheftToggle() {
        Log.d(TAG, "OTHERS_ANTITHEFT: Setting up anti-theft toggle")

        // Initialize toggle state
        binding.switchEnableAntiThief.isChecked = appViewModel.isAntiThiefEnabled()
        Log.d(TAG, "OTHERS_ANTITHEFT: Initial toggle state - ${binding.switchEnableAntiThief.isChecked}")

        // Set toggle change listener
        binding.switchEnableAntiThief.setOnCheckedChangeListener { _, isChecked ->
            Log.d(TAG, "OTHERS_ANTITHEFT: Toggle changed - $isChecked")
            handleAntiTheftToggle(isChecked)
        }

        // Set info icon click listener
        binding.antiThiefInfo.setOnClickListener {
            Log.d(TAG, "OTHERS_ANTITHEFT: Info icon clicked")
            showAntiTheftInfoDialog()
        }
    }

    /**
     * Handles anti-theft toggle state changes.
     * Shows password dialog if needed or updates preference directly.
     */
    private fun handleAntiTheftToggle(isEnabled: Boolean) {
        Log.d(TAG, "OTHERS_ANTITHEFT: Handling toggle change - enabled: $isEnabled")

        if (isEnabled && !appViewModel.isAntiThiefPasswordSet()) {
            Log.d(TAG, "OTHERS_ANTITHEFT: Enabling anti-theft but no password set - showing password dialog")
            showPasswordSetupDialog()
        } else {
            Log.d(TAG, "OTHERS_ANTITHEFT: Updating anti-theft preference - $isEnabled")
            appViewModel.setAntiThiefEnabled(isEnabled)
        }
    }

    /**
     * Shows the password setup dialog for anti-theft feature.
     */
    private fun showPasswordSetupDialog() {
        Log.d(TAG, "OTHERS_ANTITHEFT: Showing password setup dialog")

        try {
            val dialog = SetupPasswordDialog(
                context = requireContext(),
                onConfirm = { password ->
                    Log.d(TAG, "OTHERS_ANTITHEFT: Password set successfully")
                    appViewModel.setAntiThiefPassword(password)
                    appViewModel.setAntiThiefEnabled(true)
                    // Refresh toggle state to reflect the change
                    binding.switchEnableAntiThief.isChecked = true
                    Log.d(TAG, "OTHERS_ANTITHEFT: Toggle state updated after password setup")
                },
                onCancel = {
                    Log.d(TAG, "OTHERS_ANTITHEFT: Password setup cancelled")
                    // Reset toggle state if cancelled
                    binding.switchEnableAntiThief.isChecked = false
                }
            )
            dialog.show()
        } catch (e: Exception) {
            Log.e(TAG, "OTHERS_ANTITHEFT: Error showing password setup dialog", e)
            // Reset toggle state if dialog fails
            binding.switchEnableAntiThief.isChecked = false
        }
    }

    /**
     * Shows information dialog about the anti-theft feature.
     */
    private fun showAntiTheftInfoDialog() {
        Log.d(TAG, "OTHERS_ANTITHEFT: Showing anti-theft info dialog")

        val dialog = NotificationDialog(
            requireContext(),
            getString(R.string.anti_thief),
            getString(R.string.anti_thief_info)
        )
        dialog.show()

        Log.d(TAG, "OTHERS_ANTITHEFT: Anti-theft info dialog displayed")
    }

    /**
     * Observes SharedNavigationViewModel state changes for fragment self-management.
     * This fixes the button state flicker issue by ensuring proper state synchronization.
     */
    private fun observeNavigationState() {
        Log.d(TAG, "SharedNavigationViewModel: Setting up navigation state observation")
        Log.d("OthersFragment_BlankNavigation", "NAV_OBSERVATION_START: ═══════════════════════════════════════")
        Log.d("OthersFragment_BlankNavigation", "NAV_OBSERVATION_START: Setting up SharedNavigationViewModel observation")
        Log.d("OthersFragment_BlankNavigation", "NAV_OBSERVATION_START: Fragment state - isAdded: $isAdded, isVisible: $isVisible")
        Log.d("OthersFragment_BlankNavigation", "NAV_OBSERVATION_START: Fragment lifecycle: ${viewLifecycleOwner.lifecycle.currentState}")
        Log.d("OthersFragment_BlankNavigation", "NAV_OBSERVATION_START: Adapter initialized: ${::othersAdapter.isInitialized}")

        viewLifecycleOwner.lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                Log.d("OthersFragment_BlankNavigation", "NAV_FLOW_COLLECTION: Starting activeFragmentId collection")
                Log.d("OthersFragment_BlankNavigation", "NAV_FLOW_COLLECTION: Lifecycle state: ${viewLifecycleOwner.lifecycle.currentState}")

                sharedNavigationViewModel.activeFragmentId.collect { activeFragmentId ->
                    val timestamp = System.currentTimeMillis()
                    Log.d(TAG, "SharedNavigationViewModel: Navigation state changed - activeFragment: ${getFragmentName(activeFragmentId)}")

                    Log.d("OthersFragment_BlankNavigation", "NAV_STATE_CHANGE: ═══════════════════════════════════════")
                    Log.d("OthersFragment_BlankNavigation", "NAV_STATE_CHANGE: Timestamp: $timestamp")
                    Log.d("OthersFragment_BlankNavigation", "NAV_STATE_CHANGE: Active fragment ID: $activeFragmentId")
                    Log.d("OthersFragment_BlankNavigation", "NAV_STATE_CHANGE: Active fragment name: ${getFragmentName(activeFragmentId)}")
                    Log.d("OthersFragment_BlankNavigation", "NAV_STATE_CHANGE: Is Others Fragment: ${activeFragmentId == R.id.othersFragment}")
                    Log.d("OthersFragment_BlankNavigation", "NAV_STATE_CHANGE: Fragment state - isAdded: $isAdded, isVisible: $isVisible, isDetached: $isDetached")
                    Log.d("OthersFragment_BlankNavigation", "NAV_STATE_CHANGE: Fragment lifecycle: ${viewLifecycleOwner.lifecycle.currentState}")
                    Log.d("OthersFragment_BlankNavigation", "NAV_STATE_CHANGE: Adapter initialized: ${::othersAdapter.isInitialized}")
                    Log.d("OthersFragment_BlankNavigation", "NAV_STATE_CHANGE: Current adapter items: ${if (::othersAdapter.isInitialized) othersAdapter.itemCount else 0}")
                    Log.d("OthersFragment_BlankNavigation", "NAV_STATE_CHANGE: Battery observation active: $isBatteryObservationActive")
                    Log.d("OthersFragment_BlankNavigation", "NAV_STATE_CHANGE: Current charging state: $isDeviceCharging")

                    if (activeFragmentId == R.id.othersFragment) {
                        Log.d("OthersFragment_BlankNavigation", "NAV_STATE_CHANGE: Others Fragment becoming VISIBLE")
                        onFragmentVisible()
                    } else {
                        Log.d("OthersFragment_BlankNavigation", "NAV_STATE_CHANGE: Others Fragment becoming HIDDEN")
                        onFragmentHidden()
                    }
                    Log.d("OthersFragment_BlankNavigation", "NAV_STATE_CHANGE: ═══════════════════════════════════════")
                }
            }
        }
    }

    /**
     * Called when this fragment becomes visible/active.
     * Handles UI refresh and state synchronization to prevent blank fragment issues.
     * CRITICAL_FIX: Immediate battery state synchronization to prevent button text flicker.
     */
    private fun onFragmentVisible() {
        val visibilityTimestamp = System.currentTimeMillis()

        // NAVIGATION_CHAIN_TEST: Track fragment visibility after potential Health navigation
        Log.d("NAVIGATION_CHAIN_TEST", "═══════════════════════════════════════════════════════════")
        Log.d("NAVIGATION_CHAIN_TEST", "👁️ OTHERS FRAGMENT: BECOMING VISIBLE")
        Log.d("NAVIGATION_CHAIN_TEST", "═══════════════════════════════════════════════════════════")
        Log.d("NAVIGATION_CHAIN_TEST", "Visibility timestamp: $visibilityTimestamp")
        Log.d("NAVIGATION_CHAIN_TEST", "Fragment state: isAdded=$isAdded, isVisible=$isVisible, isDetached=$isDetached")
        Log.d("NAVIGATION_CHAIN_TEST", "Fragment lifecycle: ${viewLifecycleOwner.lifecycle.currentState}")
        Log.d("NAVIGATION_CHAIN_TEST", "Navigation statistics: Total=$totalClickAttempts, Success=$successfulNavigations, Failed=$failedNavigations")
        Log.d("NAVIGATION_CHAIN_TEST", "Last navigation source: $lastNavigationSource")
        Log.d("NAVIGATION_CHAIN_TEST", "Time since last back navigation: ${visibilityTimestamp - lastBackNavigationTime}ms")
        Log.d("NAVIGATION_CHAIN_TEST", "⚠️ CRITICAL: Check if this is after Health Fragment visit")

        Log.d(TAG, "SharedNavigationViewModel: OthersFragment is ACTIVE")
        Log.d(TAG, "SharedNavigationViewModel: OthersFragment is now VISIBLE")
        Log.d(TAG, "FRAGMENT_LIFECYCLE: Fragment activated via SharedNavigationViewModel")

        Log.d("OthersFragment_BlankNavigation", "FRAGMENT_VISIBLE: ═══════════════════════════════════════")
        Log.d("OthersFragment_BlankNavigation", "FRAGMENT_VISIBLE: Fragment becoming visible at: $visibilityTimestamp")
        Log.d("OthersFragment_BlankNavigation", "FRAGMENT_VISIBLE: Fragment state - isAdded: $isAdded, isVisible: $isVisible, isDetached: $isDetached")
        Log.d("OthersFragment_BlankNavigation", "FRAGMENT_VISIBLE: Fragment lifecycle: ${viewLifecycleOwner.lifecycle.currentState}")
        Log.d("OthersFragment_BlankNavigation", "FRAGMENT_VISIBLE: Adapter initialized: ${::othersAdapter.isInitialized}")
        Log.d("OthersFragment_BlankNavigation", "FRAGMENT_VISIBLE: Current adapter items: ${if (::othersAdapter.isInitialized) othersAdapter.itemCount else 0}")
        Log.d("OthersFragment_BlankNavigation", "FRAGMENT_VISIBLE: Battery observation active: $isBatteryObservationActive")
        Log.d("OthersFragment_BlankNavigation", "FRAGMENT_VISIBLE: Current charging state: $isDeviceCharging")
        Log.d("OthersFragment_BlankNavigation", "FRAGMENT_VISIBLE: Pending UI update job exists: ${pendingUIUpdateJob != null}")
        Log.d("OthersFragment_BlankNavigation", "FRAGMENT_VISIBLE: RecyclerView adapter attached: ${binding.othersRecyclerView.adapter != null}")

        // CRITICAL_FIX: Cancel any pending updates to prevent conflicts
        Log.d("OthersFragment_BlankNavigation", "FRAGMENT_VISIBLE: Cancelling existing pending UI update job")
        pendingUIUpdateJob?.cancel()

        // CRITICAL_FIX: Immediate battery state synchronization to prevent button text flicker
        Log.d("OthersFragment_ButtonFlicker", "IMMEDIATE_SYNC: ═══════════════════════════════════════")
        Log.d("OthersFragment_ButtonFlicker", "IMMEDIATE_SYNC: Performing immediate battery state synchronization")
        Log.d("OthersFragment_ButtonFlicker", "IMMEDIATE_SYNC: Pre-sync charging state: $isDeviceCharging")

        // CRITICAL_FIX: Get current battery status immediately and synchronize to prevent flicker
        val currentBatteryStatus = coreBatteryStatsProvider.getCurrentStatus()
        if (currentBatteryStatus != null) {
            val previousState = isDeviceCharging
            isDeviceCharging = currentBatteryStatus.isCharging
            lastBatteryStateUpdateTime = visibilityTimestamp // Update timestamp to prevent debouncing conflicts

            Log.d("OthersFragment_ButtonFlicker", "IMMEDIATE_SYNC: Battery status retrieved - charging: ${currentBatteryStatus.isCharging}")
            Log.d("OthersFragment_ButtonFlicker", "IMMEDIATE_SYNC: State change: $previousState → $isDeviceCharging")
            Log.d("OthersFragment_ButtonFlicker", "IMMEDIATE_SYNC: Update timestamp set to: $lastBatteryStateUpdateTime")

            if (previousState != isDeviceCharging) {
                Log.w("OthersFragment_ButtonFlicker", "IMMEDIATE_SYNC: *** CRITICAL STATE MISMATCH DETECTED ***")
                Log.w("OthersFragment_ButtonFlicker", "IMMEDIATE_SYNC: This would have caused button text flicker!")
                Log.d("OthersFragment_ButtonFlicker", "IMMEDIATE_SYNC: ✅ FLICKER PREVENTION: State corrected at fragment visibility")
            } else {
                Log.d("OthersFragment_ButtonFlicker", "IMMEDIATE_SYNC: State consistent, no flicker risk")
            }
        } else {
            Log.w("OthersFragment_ButtonFlicker", "IMMEDIATE_SYNC: No current battery status available")
            Log.w("OthersFragment_ButtonFlicker", "IMMEDIATE_SYNC: Keeping existing state: $isDeviceCharging")
        }
        Log.d("OthersFragment_ButtonFlicker", "IMMEDIATE_SYNC: Final charging state: $isDeviceCharging")
        Log.d("OthersFragment_ButtonFlicker", "IMMEDIATE_SYNC: ═══════════════════════════════════════")

        // CRITICAL_FIX: Immediate UI update with correct state (no delay)
        pendingUIUpdateJob = viewLifecycleOwner.lifecycleScope.launch {
            Log.d("OthersFragment_BlankNavigation", "FRAGMENT_VISIBLE: Immediate UI update (no delay)")
            Log.d("OthersFragment_BlankNavigation", "FRAGMENT_VISIBLE: Fragment state: isAdded=$isAdded, isVisible=$isVisible")
            Log.d("OthersFragment_BlankNavigation", "FRAGMENT_VISIBLE: Adapter initialized: ${::othersAdapter.isInitialized}")
            Log.d("OthersFragment_BlankNavigation", "FRAGMENT_VISIBLE: Synchronized charging state: $isDeviceCharging")

            // Immediate UI refresh with synchronized state
            if (::othersAdapter.isInitialized) {
                Log.d(TAG, "SharedNavigationViewModel: Immediate adapter refresh with synchronized state")
                Log.d("OthersFragment_BlankNavigation", "FRAGMENT_VISIBLE: Adapter is initialized, immediate refresh")
                Log.d("OthersFragment_BlankNavigation", "FRAGMENT_VISIBLE: Pre-refresh adapter items: ${othersAdapter.itemCount}")
                updateAdapterItems()
                Log.d("OthersFragment_BlankNavigation", "FRAGMENT_VISIBLE: Post-refresh adapter items: ${othersAdapter.itemCount}")
            } else {
                Log.w("OthersFragment_BlankNavigation", "FRAGMENT_VISIBLE: Adapter not initialized, cannot refresh items")
            }

            // Ensure battery observation is active
            if (!isBatteryObservationActive) {
                Log.d(TAG, "SharedNavigationViewModel: Restarting battery observation")
                Log.d("OthersFragment_BlankNavigation", "FRAGMENT_VISIBLE: Battery observation inactive, restarting")
                observeBatteryStatus()
            } else {
                Log.d("OthersFragment_BlankNavigation", "FRAGMENT_VISIBLE: Battery observation already active")
            }

            Log.d("OthersFragment_BlankNavigation", "FRAGMENT_VISIBLE: Fragment visibility handling completed")
        }
        Log.d("OthersFragment_BlankNavigation", "FRAGMENT_VISIBLE: ═══════════════════════════════════════")
    }

    /**
     * Called when this fragment becomes hidden/inactive.
     * Handles cleanup and state preservation.
     */
    private fun onFragmentHidden() {
        val hiddenTimestamp = System.currentTimeMillis()
        Log.d(TAG, "SharedNavigationViewModel: OthersFragment is INACTIVE")
        Log.d(TAG, "SharedNavigationViewModel: OthersFragment is now HIDDEN")

        Log.d("OthersFragment_BlankNavigation", "FRAGMENT_HIDDEN: ═══════════════════════════════════════")
        Log.d("OthersFragment_BlankNavigation", "FRAGMENT_HIDDEN: Fragment becoming hidden at: $hiddenTimestamp")
        Log.d("OthersFragment_BlankNavigation", "FRAGMENT_HIDDEN: Fragment state - isAdded: $isAdded, isVisible: $isVisible, isDetached: $isDetached")
        Log.d("OthersFragment_BlankNavigation", "FRAGMENT_HIDDEN: Fragment lifecycle: ${viewLifecycleOwner.lifecycle.currentState}")
        Log.d("OthersFragment_BlankNavigation", "FRAGMENT_HIDDEN: Adapter initialized: ${::othersAdapter.isInitialized}")
        Log.d("OthersFragment_BlankNavigation", "FRAGMENT_HIDDEN: Current adapter items: ${if (::othersAdapter.isInitialized) othersAdapter.itemCount else 0}")
        Log.d("OthersFragment_BlankNavigation", "FRAGMENT_HIDDEN: Battery observation active: $isBatteryObservationActive")
        Log.d("OthersFragment_BlankNavigation", "FRAGMENT_HIDDEN: Pending UI update job exists: ${pendingUIUpdateJob != null}")

        // Cancel any pending UI updates when fragment becomes inactive
        Log.d("OthersFragment_BlankNavigation", "FRAGMENT_HIDDEN: Cancelling pending UI updates")
        pendingUIUpdateJob?.cancel()
        Log.d(TAG, "SharedNavigationViewModel: Cancelled pending UI updates")
        Log.d("OthersFragment_BlankNavigation", "FRAGMENT_HIDDEN: Pending UI updates cancelled")
        Log.d("OthersFragment_BlankNavigation", "FRAGMENT_HIDDEN: Fragment hidden handling completed")
        Log.d("OthersFragment_BlankNavigation", "FRAGMENT_HIDDEN: ═══════════════════════════════════════")
    }

    /**
     * Helper method to get human-readable fragment names for debugging.
     */
    private fun getFragmentName(fragmentId: Int): String {
        return when (fragmentId) {
            R.id.animationGridFragment -> "AnimationGridFragment"
            R.id.othersFragment -> "OthersFragment"
            R.id.chargeFragment -> "ChargeFragment"
            R.id.dischargeFragment -> "DischargeFragment"
            R.id.healthFragment -> "HealthFragment"
            R.id.settingsFragment -> "SettingsFragment"
            else -> "Unknown($fragmentId)"
        }
    }
}