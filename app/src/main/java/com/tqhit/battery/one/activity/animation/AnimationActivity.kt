package com.tqhit.battery.one.activity.animation

import android.widget.Toast
import com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity
import com.tqhit.battery.one.databinding.ActivityAnimationBinding
import dagger.hilt.android.AndroidEntryPoint
import androidx.media3.common.MediaItem
import androidx.media3.exoplayer.ExoPlayer
import com.tqhit.battery.one.R
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.BatteryManager
import android.os.Handler
import android.os.Looper
import android.view.View
import com.tqhit.battery.one.utils.DateTimeUtils
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.annotation.OptIn
import androidx.core.content.ContextCompat
import androidx.core.net.toUri
import androidx.lifecycle.lifecycleScope
import androidx.media3.common.util.UnstableApi
import androidx.media3.ui.AspectRatioFrameLayout
import com.tqhit.adlib.sdk.firebase.FirebaseRemoteConfigHelper
import com.tqhit.battery.one.ads.core.ApplovinInterstitialAdManager
import com.tqhit.battery.one.ads.core.ApplovinRewardedAdManager
import com.tqhit.battery.one.dialog.utils.NotificationDialog
import com.tqhit.battery.one.dialog.utils.PostAnimationDialog
import com.tqhit.battery.one.repository.AppRepository
import com.tqhit.battery.one.utils.BatteryLogger
import com.tqhit.battery.one.utils.OverlayPermissionUtils
import com.tqhit.battery.one.viewmodel.animation.AnimationViewModel
import kotlinx.coroutines.delay
import javax.inject.Inject

@AndroidEntryPoint
class AnimationActivity : AdLibBaseActivity<ActivityAnimationBinding>() {
    override val binding by lazy { ActivityAnimationBinding.inflate(layoutInflater) }

    @Inject lateinit var remoteConfigHelper: FirebaseRemoteConfigHelper
    @Inject lateinit var applovinRewardedAdManager: ApplovinRewardedAdManager
    private val animationViewModel: AnimationViewModel by viewModels()
    private var mediaUrl = ""
    @Inject
    lateinit var applovinInterstitialAdManager: ApplovinInterstitialAdManager

    private var player: ExoPlayer? = null
    private val timeHandler = Handler(Looper.getMainLooper())
    private val timeRunnable = object : Runnable {
        override fun run() {
            updateTimeAndDate()
            timeHandler.postDelayed(this, 60_000L)
        }
    }
    private val batteryReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            val level = intent?.getIntExtra(BatteryManager.EXTRA_LEVEL, -1) ?: -1
            if (level >= 0) {
                binding.batteryPercent.text = "$level%"
            }
        }
    }

    private lateinit var overlayPermissionLauncher: ActivityResultLauncher<Intent>
    @Inject lateinit var appRepository: AppRepository
    @Inject lateinit var videoUtils: com.tqhit.battery.one.utils.VideoUtils

    override fun setupData() {
        super.setupData()
        overlayPermissionLauncher = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            if (!android.provider.Settings.canDrawOverlays(this)) {
                Toast.makeText(this, getString(R.string.overlay_permission_denied), Toast.LENGTH_SHORT).show()
            }
        }
        binding.dateTimeContainer.visibility = if (appRepository.isAnimationOverlayTimeEnabled())
            View.VISIBLE else View.GONE

        val rvLoadInView = remoteConfigHelper.getBoolean("rv_load_in_view")
        if (rvLoadInView) {
            applovinRewardedAdManager.loadRewardedAd()
        }
    }

    override fun setupUI() {
        super.setupUI()
        val videoUrl = intent.getStringExtra("video_url") ?: run {
            Toast.makeText(this, getString(R.string.no_video_url_provided), Toast.LENGTH_SHORT).show()
            finish()
            return
        }
        mediaUrl = videoUrl
        setupPlayer(videoUrl)
        // Update time/date immediately and start handler
        updateTimeAndDate()
        timeHandler.post(timeRunnable)
        // Register battery receiver
        registerReceiver(batteryReceiver, IntentFilter(Intent.ACTION_BATTERY_CHANGED))
        updateButton()
        startTimer()
    }

    override fun setupListener() {
        super.setupListener()

        binding.backButton.setOnClickListener {
            setupInterstitialAd()
        }
        binding.applyButton.setOnClickListener {
            if (animationViewModel.isApplied(mediaUrl)) return@setOnClickListener

            BatteryLogger.d("AnimationActivity", "Apply button clicked for animation: $mediaUrl")

            // Check overlay permission using centralized utility
            if (!OverlayPermissionUtils.isOverlayPermissionGranted(this)) {
                BatteryLogger.d("AnimationActivity", "Overlay permission not granted, showing permission dialog")
                OverlayPermissionUtils.showOverlayPermissionDialog(
                    context = this,
                    onConfirm = {
                        val intent = OverlayPermissionUtils.createOverlayPermissionIntent(this)
                        overlayPermissionLauncher.launch(intent)
                    },
                    onCancel = {
                        BatteryLogger.d("AnimationActivity", "User cancelled overlay permission request")
                    }
                )
                return@setOnClickListener
            }
            if (!animationViewModel.isExpired(mediaUrl)) {
                apply()
                updateButton()
            } else {
                applovinRewardedAdManager.showRewardedAd(
                    this,
                    "animation_trial"
                ) {
                    animationViewModel.applyAnimation(mediaUrl)
                    apply()
                    updateButton()
                }
            }
        }
    }

    private fun setupInterstitialAd(){
        applovinInterstitialAdManager.showInterstitialAd(
            "default_iv",
            this,
            onNext = {
                finish()
            }
        )
    }



    private fun updateButton() {
        val isExpired = animationViewModel.isExpired(mediaUrl)
        val isApplied = animationViewModel.isApplied(mediaUrl)
        binding.applyButton.background = ContextCompat.getDrawable(this, R.drawable.colorr_button)
        if (!isExpired) {
            binding.timeRemaining.visibility = View.VISIBLE
            binding.timeRemainingValue.visibility = View.VISIBLE
            binding.iconAd.visibility = View.GONE
            binding.textBtn.text = getString(R.string.apply)
            binding.textBtn.setTextColor(getThemeColor(R.attr.grey))
            if (isApplied) {
                binding.applyButton.background = ContextCompat.getDrawable(this, R.drawable.grey_button)
                binding.textBtn.setTextColor(getThemeColor(R.attr.black))
                binding.textBtn.text = getString(R.string.applied)
            }
        } else {
            binding.iconAd.visibility = View.VISIBLE
            binding.textBtn.text = getString(R.string.apply_for_24hrs)
            binding.timeRemaining.visibility = View.GONE
            binding.timeRemainingValue.visibility = View.GONE
        }
    }

    private fun startTimer() {
        lifecycleScope.launch {
            while (true) {
                binding.timeRemainingValue.text = DateTimeUtils.formatMillisToTimeString(animationViewModel.getTimeRemaining(mediaUrl))
                delay(1000L)
            }
        }
    }

    private fun apply() {
        BatteryLogger.d("AnimationActivity", "Apply method called")

        // Check overlay permission using centralized utility
        if (!OverlayPermissionUtils.isOverlayPermissionGranted(this)) {
            BatteryLogger.d("AnimationActivity", "Overlay permission not granted in apply method")
            OverlayPermissionUtils.showOverlayPermissionDialog(
                context = this,
                onConfirm = {
                    val intent = OverlayPermissionUtils.createOverlayPermissionIntent(this)
                    overlayPermissionLauncher.launch(intent)
                },
                onCancel = {
                    BatteryLogger.d("AnimationActivity", "User cancelled overlay permission request in apply method")
                }
            )
        } else {
            BatteryLogger.d("AnimationActivity", "Overlay permission granted, proceeding with animation application")
            proceedAfterOverlayPermission()
        }
    }

    @OptIn(UnstableApi::class)
    private fun setupPlayer(videoUrl: String) {
        val playerView = binding.playerView
        val exoPlayer = ExoPlayer.Builder(this).build().also { player = it }
        playerView.player = exoPlayer
        playerView.useController = false // Hide all controls
        playerView.resizeMode = AspectRatioFrameLayout.RESIZE_MODE_ZOOM
        val mediaItem = MediaItem.fromUri(videoUrl.toUri())
        exoPlayer.setMediaItem(mediaItem)
        exoPlayer.repeatMode = ExoPlayer.REPEAT_MODE_ONE
        exoPlayer.prepare()
        exoPlayer.playWhenReady = true
        // Listen for error
        exoPlayer.addListener(object : androidx.media3.common.Player.Listener {
            override fun onPlayerError(error: androidx.media3.common.PlaybackException) {
                Toast.makeText(this@AnimationActivity,
                    getString(R.string.cannot_load_video), Toast.LENGTH_SHORT).show()
                finish()
            }
        })
    }

    private fun updateTimeAndDate() {
        binding.textTime.text = DateTimeUtils.getCurrentTimeString()
        binding.textDate.text = DateTimeUtils.getCurrentDateString()
    }

    private fun proceedAfterOverlayPermission() {
        val loadingDialog = com.tqhit.battery.one.dialog.utils.LoadingDialog(this, getString(R.string.saving_video))
        loadingDialog.show()
        val videoUrl = intent.getStringExtra("video_url")
        if (videoUrl == null) {
            if (loadingDialog.isShowing && !isFinishing && !isDestroyed) {
                loadingDialog.dismiss()
            }
            if (!isFinishing && !isDestroyed) {
                Toast.makeText(this, getString(R.string.no_video_url_provided), Toast.LENGTH_SHORT).show()
            }
            return
        }
        lifecycleScope.launch {
            try {
                // Use enhanced video utils for better performance with preloaded files
                val destFile = videoUtils.downloadAndSaveVideoEnhanced(videoUrl)
                appRepository.setVideoPath(destFile.absolutePath)
                appRepository.setAnimationOverlayEnabled(true)
                if (loadingDialog.isShowing && !isFinishing && !isDestroyed) {
                    loadingDialog.dismiss()
                }
                if (!isFinishing && !isDestroyed) {
                    BatteryLogger.d("AnimationActivity", "Animation successfully applied: $mediaUrl")
                    Toast.makeText(this@AnimationActivity, getString(R.string.video_saved), Toast.LENGTH_SHORT).show()

                    // Show post-animation dialog since overlay permission is already granted
                    BatteryLogger.d("AnimationActivity", "Showing post-animation instruction dialog")
                    PostAnimationDialog.showDialog(this@AnimationActivity) {
                        BatteryLogger.d("AnimationActivity", "Post-animation dialog dismissed, finishing activity")
                        finish()
                    }
                } else {
                    finish()
                }
                animationViewModel.setTrialEndTime(mediaUrl)
                animationViewModel.setApplied(mediaUrl)
            } catch (e: Exception) {
                if (loadingDialog.isShowing && !isFinishing && !isDestroyed) {
                    loadingDialog.dismiss()
                }
                if (!isFinishing && !isDestroyed) {
                    Toast.makeText(this@AnimationActivity, getString(R.string.error_accessing_file), Toast.LENGTH_SHORT).show()
                }
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        player?.release()
        player = null
        // Remove callbacks and unregister receiver
        timeHandler.removeCallbacks(timeRunnable)
        try { unregisterReceiver(batteryReceiver) } catch (_: Exception) {}
    }

    private fun getThemeColor(attr: Int): Int {
        val typedValue = android.util.TypedValue()
        val theme = this.theme
        theme.resolveAttribute(attr, typedValue, true)
        return typedValue.data
    }
}