package com.tqhit.battery.one.utils

import com.tqhit.battery.one.repository.AnimationPreloadingRepository
import com.tqhit.battery.one.repository.PreloadingResult
import com.tqhit.battery.one.repository.PreloadingStats
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Monitoring utility for animation preloading operations.
 * Provides structured logging and performance metrics for debugging and optimization.
 * 
 * Following SOLID principles:
 * - Single Responsibility: Only handles monitoring and logging
 * - Open/Closed: Extensible for different monitoring strategies
 * - Dependency Inversion: Depends on abstractions
 */
@Singleton
class PreloadingMonitor @Inject constructor(
    private val animationPreloadingRepository: AnimationPreloadingRepository
) {
    companion object {
        private const val TAG = "PreloadingMonitor"
        
        // Performance thresholds for monitoring
        private const val SLOW_PRELOAD_THRESHOLD_MS = 30_000L // 30 seconds
        private const val LARGE_FILE_THRESHOLD_BYTES = 10 * 1024 * 1024L // 10 MB
        private const val MAX_REASONABLE_FILE_COUNT = 20
    }
    
    /**
     * Logs the start of a preloading operation with context information.
     */
    fun logPreloadingStart(animationCount: Int, reason: String = "App startup") {
        BatteryLogger.d(TAG, "PRELOAD_START: Starting preloading operation")
        BatteryLogger.d(TAG, "PRELOAD_START: Animation count: $animationCount")
        BatteryLogger.d(TAG, "PRELOAD_START: Reason: $reason")
        BatteryLogger.d(TAG, "PRELOAD_START: Timestamp: ${System.currentTimeMillis()}")
    }
    
    /**
     * Logs the completion of a preloading operation with detailed results.
     */
    fun logPreloadingCompletion(
        result: PreloadingResult,
        durationMs: Long,
        animationCount: Int
    ) {
        BatteryLogger.d(TAG, "PRELOAD_COMPLETE: Preloading operation completed")
        BatteryLogger.d(TAG, "PRELOAD_COMPLETE: Duration: ${durationMs}ms")
        BatteryLogger.d(TAG, "PRELOAD_COMPLETE: Animation count: $animationCount")
        BatteryLogger.d(TAG, "PRELOAD_COMPLETE: Result type: ${result::class.simpleName}")
        
        // Log performance warnings
        if (durationMs > SLOW_PRELOAD_THRESHOLD_MS) {
            BatteryLogger.w(TAG, "PRELOAD_PERFORMANCE: Slow preloading detected - ${durationMs}ms for $animationCount animations")
        }
        
        // Log detailed result information
        when (result) {
            is PreloadingResult.Success -> {
                BatteryLogger.d(TAG, "PRELOAD_COMPLETE: Successfully preloaded ${result.preloadedCount} animations")
                logPreloadingEfficiency(result.preloadedCount, animationCount, durationMs)
            }
            is PreloadingResult.PartialSuccess -> {
                BatteryLogger.w(TAG, "PRELOAD_COMPLETE: Partial success - ${result.successCount} succeeded, ${result.failures.size} failed")
                logPreloadingFailures(result.failures)
                logPreloadingEfficiency(result.successCount, animationCount, durationMs)
            }
            is PreloadingResult.AllFailed -> {
                BatteryLogger.e(TAG, "PRELOAD_COMPLETE: All preloading failed - ${result.failures.size} failures")
                logPreloadingFailures(result.failures)
            }
            is PreloadingResult.AlreadyUpToDate -> {
                BatteryLogger.d(TAG, "PRELOAD_COMPLETE: Skipped - already up to date")
            }
            is PreloadingResult.NoAnimationsProvided -> {
                BatteryLogger.w(TAG, "PRELOAD_COMPLETE: No animations provided for preloading")
            }
            is PreloadingResult.Error -> {
                BatteryLogger.e(TAG, "PRELOAD_COMPLETE: Error during preloading", result.exception)
            }
        }
    }
    
    /**
     * Logs preloading efficiency metrics.
     */
    private fun logPreloadingEfficiency(successCount: Int, totalCount: Int, durationMs: Long) {
        val successRate = if (totalCount > 0) (successCount * 100.0 / totalCount) else 0.0
        val avgTimePerAnimation = if (successCount > 0) durationMs / successCount else 0L
        
        BatteryLogger.d(TAG, "PRELOAD_EFFICIENCY: Success rate: ${"%.1f".format(successRate)}%")
        BatteryLogger.d(TAG, "PRELOAD_EFFICIENCY: Average time per animation: ${avgTimePerAnimation}ms")
        
        if (successRate < 50.0) {
            BatteryLogger.w(TAG, "PRELOAD_EFFICIENCY: Low success rate detected: ${"%.1f".format(successRate)}%")
        }
        
        if (avgTimePerAnimation > 10_000L) { // 10 seconds per animation
            BatteryLogger.w(TAG, "PRELOAD_EFFICIENCY: Slow average download time: ${avgTimePerAnimation}ms per animation")
        }
    }
    
    /**
     * Logs detailed failure information for debugging.
     */
    private fun logPreloadingFailures(failures: List<com.tqhit.battery.one.fragment.main.animation.data.PreloadResult.Failure>) {
        BatteryLogger.w(TAG, "PRELOAD_FAILURES: Analyzing ${failures.size} failures")
        
        val failuresByType = failures.groupBy { failure ->
            when {
                failure.errorMessage.contains("network", ignoreCase = true) -> "Network"
                failure.errorMessage.contains("invalid", ignoreCase = true) -> "Invalid URL"
                failure.errorMessage.contains("storage", ignoreCase = true) -> "Storage"
                failure.errorMessage.contains("validation", ignoreCase = true) -> "Validation"
                else -> "Other"
            }
        }
        
        failuresByType.forEach { (type, typeFailures) ->
            BatteryLogger.w(TAG, "PRELOAD_FAILURES: $type failures: ${typeFailures.size}")
            typeFailures.take(3).forEach { failure -> // Log first 3 of each type
                BatteryLogger.w(TAG, "PRELOAD_FAILURES: $type - ${failure.mediaOriginal}: ${failure.errorMessage}")
            }
        }
    }
    
    /**
     * Logs current preloading statistics for monitoring.
     */
    suspend fun logCurrentStats() = withContext(Dispatchers.IO) {
        try {
            val stats = animationPreloadingRepository.getPreloadingStats()
            logPreloadingStats(stats)
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Error logging current stats", e)
        }
    }
    
    /**
     * Logs detailed preloading statistics.
     */
    private fun logPreloadingStats(stats: PreloadingStats) {
        BatteryLogger.d(TAG, "PRELOAD_STATS: Current preloading statistics")
        BatteryLogger.d(TAG, "PRELOAD_STATS: File count: ${stats.preloadedFileCount}")
        BatteryLogger.d(TAG, "PRELOAD_STATS: Total size: ${formatFileSize(stats.totalSizeBytes)}")
        BatteryLogger.d(TAG, "PRELOAD_STATS: Last preload: ${formatTimestamp(stats.lastPreloadTimestamp)}")
        
        // Log warnings for unusual conditions
        if (stats.preloadedFileCount > MAX_REASONABLE_FILE_COUNT) {
            BatteryLogger.w(TAG, "PRELOAD_STATS: High file count detected: ${stats.preloadedFileCount}")
        }
        
        if (stats.totalSizeBytes > LARGE_FILE_THRESHOLD_BYTES) {
            BatteryLogger.w(TAG, "PRELOAD_STATS: Large total size detected: ${formatFileSize(stats.totalSizeBytes)}")
        }
        
        val timeSinceLastPreload = System.currentTimeMillis() - stats.lastPreloadTimestamp
        if (timeSinceLastPreload > 7 * 24 * 60 * 60 * 1000L) { // 7 days
            BatteryLogger.w(TAG, "PRELOAD_STATS: Old preload data detected: ${formatDuration(timeSinceLastPreload)} ago")
        }
    }
    
    /**
     * Logs individual animation preloading attempt.
     */
    fun logAnimationPreloadAttempt(mediaUrl: String, isPreloaded: Boolean) {
        if (isPreloaded) {
            BatteryLogger.d(TAG, "PRELOAD_USAGE: Using preloaded file for: $mediaUrl")
        } else {
            BatteryLogger.d(TAG, "PRELOAD_USAGE: Falling back to network download for: $mediaUrl")
        }
    }
    
    /**
     * Logs preloading cache hit/miss statistics.
     */
    fun logCacheHitRate(hits: Int, misses: Int) {
        val total = hits + misses
        if (total > 0) {
            val hitRate = (hits * 100.0 / total)
            BatteryLogger.d(TAG, "PRELOAD_CACHE: Hit rate: ${"%.1f".format(hitRate)}% ($hits/$total)")
            
            if (hitRate < 30.0) {
                BatteryLogger.w(TAG, "PRELOAD_CACHE: Low cache hit rate: ${"%.1f".format(hitRate)}%")
            }
        }
    }
    
    /**
     * Logs system resource usage during preloading.
     */
    fun logResourceUsage(memoryUsageMB: Long, networkUsageKB: Long) {
        BatteryLogger.d(TAG, "PRELOAD_RESOURCES: Memory usage: ${memoryUsageMB}MB")
        BatteryLogger.d(TAG, "PRELOAD_RESOURCES: Network usage: ${networkUsageKB}KB")
        
        if (memoryUsageMB > 100) { // 100MB threshold
            BatteryLogger.w(TAG, "PRELOAD_RESOURCES: High memory usage detected: ${memoryUsageMB}MB")
        }
    }
    
    /**
     * Formats file size in human-readable format.
     */
    private fun formatFileSize(bytes: Long): String {
        return when {
            bytes < 1024 -> "${bytes}B"
            bytes < 1024 * 1024 -> "${"%.1f".format(bytes / 1024.0)}KB"
            bytes < 1024 * 1024 * 1024 -> "${"%.1f".format(bytes / (1024.0 * 1024.0))}MB"
            else -> "${"%.1f".format(bytes / (1024.0 * 1024.0 * 1024.0))}GB"
        }
    }
    
    /**
     * Formats timestamp in human-readable format.
     */
    private fun formatTimestamp(timestamp: Long): String {
        return if (timestamp == 0L) {
            "Never"
        } else {
            val now = System.currentTimeMillis()
            val diff = now - timestamp
            formatDuration(diff) + " ago"
        }
    }
    
    /**
     * Formats duration in human-readable format.
     */
    private fun formatDuration(durationMs: Long): String {
        val seconds = durationMs / 1000
        val minutes = seconds / 60
        val hours = minutes / 60
        val days = hours / 24
        
        return when {
            days > 0 -> "${days}d ${hours % 24}h"
            hours > 0 -> "${hours}h ${minutes % 60}m"
            minutes > 0 -> "${minutes}m ${seconds % 60}s"
            else -> "${seconds}s"
        }
    }
}
