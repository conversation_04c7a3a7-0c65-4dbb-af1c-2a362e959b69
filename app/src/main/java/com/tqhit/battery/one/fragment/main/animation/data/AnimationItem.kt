package com.tqhit.battery.one.fragment.main.animation.data

import com.google.gson.annotations.SerializedName

data class AnimationItem(
    @SerializedName("isPremium") val isPremium: Boolean,
    @SerializedName("mediaOriginal") val mediaOriginal: String,
    @SerializedName("thumbnail") val thumbnail: String
)

/**
 * Represents the preload status of an animation item
 */
enum class PreloadStatus {
    NOT_STARTED,    // Preloading has not been initiated
    IN_PROGRESS,    // Currently downloading
    COMPLETED,      // Successfully downloaded and available locally
    FAILED,         // Download failed
    EXPIRED         // Local file exists but may be outdated
}

/**
 * Data class representing a preloaded animation with metadata
 */
data class PreloadedAnimationItem(
    val mediaOriginal: String,
    val localFilePath: String,
    val status: PreloadStatus,
    val downloadTimestamp: Long,
    val fileSizeBytes: Long,
    val errorMessage: String? = null
)

/**
 * Sealed class representing the result of a preload operation
 */
sealed class PreloadResult {
    data class Success(
        val preloadedItem: PreloadedAnimationItem
    ) : PreloadResult()

    data class Failure(
        val mediaOriginal: String,
        val errorMessage: String,
        val exception: Throwable? = null
    ) : PreloadResult()

    data class AlreadyExists(
        val preloadedItem: PreloadedAnimationItem
    ) : PreloadResult()
}