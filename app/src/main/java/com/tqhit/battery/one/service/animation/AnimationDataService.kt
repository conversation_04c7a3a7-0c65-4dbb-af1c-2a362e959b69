package com.tqhit.battery.one.service.animation

import com.google.gson.Gson
import com.tqhit.adlib.sdk.firebase.FirebaseRemoteConfigHelper
import com.tqhit.battery.one.fragment.main.animation.data.AnimationCategory
import com.tqhit.battery.one.fragment.main.animation.data.AnimationItem
import com.tqhit.battery.one.utils.BatteryLogger
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Service responsible for fetching and parsing animation data from Firebase Remote Config.
 * Provides animation data for preloading operations.
 * 
 * Following SOLID principles:
 * - Single Responsibility: Only handles animation data fetching and parsing
 * - Open/Closed: Extensible for different data sources
 * - Dependency Inversion: Depends on abstractions (FirebaseRemoteConfigHelper)
 */
@Singleton
class AnimationDataService @Inject constructor(
    private val remoteConfigHelper: FirebaseRemoteConfigHelper,
    private val gson: <PERSON><PERSON>
) {
    companion object {
        private const val TAG = "AnimationDataService"
        private const val ANIMATION_JSON_KEY = "animation_json"
        private const val PRELOAD_COUNT = 6
    }
    
    /**
     * Fetches and parses animation data from Firebase Remote Config.
     * Returns the first 6 animations suitable for preloading.
     */
    suspend fun getAnimationsForPreloading(): List<AnimationItem> = withContext(Dispatchers.IO) {
        try {
            BatteryLogger.d(TAG, "Fetching animation data for preloading")
            
            val jsonString = remoteConfigHelper.getString(ANIMATION_JSON_KEY)
            if (jsonString.isBlank()) {
                BatteryLogger.w(TAG, "Empty animation JSON from remote config")
                return@withContext emptyList()
            }
            
            BatteryLogger.d(TAG, "Retrieved animation JSON string length: ${jsonString.length}")
            
            val categories = parseAnimationCategories(jsonString)
            if (categories.isEmpty()) {
                BatteryLogger.w(TAG, "No valid animation categories parsed")
                return@withContext emptyList()
            }
            
            // Extract first 6 animations from all categories
            val allAnimations = categories.flatMap { it.content }
            val animationsForPreload = allAnimations.take(PRELOAD_COUNT)
            
            BatteryLogger.d(TAG, "Found ${allAnimations.size} total animations, selecting ${animationsForPreload.size} for preloading")
            
            // Log the animations being selected for preloading
            animationsForPreload.forEachIndexed { index, animation ->
                BatteryLogger.d(TAG, "Preload animation ${index + 1}: ${animation.mediaOriginal} (Premium: ${animation.isPremium})")
            }
            
            animationsForPreload
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Error fetching animations for preloading", e)
            emptyList()
        }
    }
    
    /**
     * Parses animation categories from JSON string.
     * Validates and filters out invalid categories.
     */
    private fun parseAnimationCategories(jsonString: String): List<AnimationCategory> {
        return try {
            val categories = gson.fromJson(jsonString, Array<AnimationCategory>::class.java).toList()
            BatteryLogger.d(TAG, "Successfully parsed ${categories.size} categories")
            
            // Validate parsed data
            val validCategories = categories.filter { category ->
                val isValid = category.name.isNotBlank() && 
                             category.content.isNotEmpty() &&
                             category.content.all { isValidAnimationItem(it) }
                
                if (!isValid) {
                    BatteryLogger.w(TAG, "Invalid category filtered out: ${category.name}")
                }
                
                isValid
            }
            
            if (validCategories.size != categories.size) {
                BatteryLogger.w(TAG, "Filtered out ${categories.size - validCategories.size} invalid categories")
            }
            
            validCategories
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Error parsing animation categories", e)
            emptyList()
        }
    }
    
    /**
     * Validates an individual animation item.
     */
    private fun isValidAnimationItem(item: AnimationItem): Boolean {
        val isValid = item.mediaOriginal.isNotBlank() && 
                     item.thumbnail.isNotBlank() &&
                     isValidUrl(item.mediaOriginal) &&
                     isValidUrl(item.thumbnail)
        
        if (!isValid) {
            BatteryLogger.w(TAG, "Invalid animation item: ${item.mediaOriginal}")
        }
        
        return isValid
    }
    
    /**
     * Basic URL validation.
     */
    private fun isValidUrl(url: String): Boolean {
        return url.startsWith("http://") || url.startsWith("https://")
    }
    
    /**
     * Gets all animation data (for use by Animation Fragment).
     * This method can be used by the existing AnimationGridFragment.
     */
    suspend fun getAllAnimationCategories(): List<AnimationCategory> = withContext(Dispatchers.IO) {
        try {
            BatteryLogger.d(TAG, "Fetching all animation categories")
            
            val jsonString = remoteConfigHelper.getString(ANIMATION_JSON_KEY)
            if (jsonString.isBlank()) {
                BatteryLogger.w(TAG, "Empty animation JSON from remote config")
                return@withContext createFallbackCategories()
            }
            
            val categories = parseAnimationCategories(jsonString)
            if (categories.isEmpty()) {
                BatteryLogger.w(TAG, "No valid categories, using fallback")
                return@withContext createFallbackCategories()
            }
            
            BatteryLogger.d(TAG, "Successfully retrieved ${categories.size} animation categories")
            categories
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Error fetching all animation categories", e)
            createFallbackCategories()
        }
    }
    
    /**
     * Creates fallback categories when remote config fails.
     * This ensures the app doesn't break if remote config is unavailable.
     */
    private fun createFallbackCategories(): List<AnimationCategory> {
        BatteryLogger.d(TAG, "Creating fallback animation categories")
        
        // Create minimal fallback data
        val fallbackAnimations = listOf(
            AnimationItem(
                isPremium = false,
                mediaOriginal = "https://example.com/fallback1.mp4",
                thumbnail = "https://example.com/fallback1_thumb.jpg"
            ),
            AnimationItem(
                isPremium = false,
                mediaOriginal = "https://example.com/fallback2.mp4",
                thumbnail = "https://example.com/fallback2_thumb.jpg"
            )
        )
        
        return listOf(
            AnimationCategory(
                name = "Default",
                content = fallbackAnimations
            )
        )
    }
    
    /**
     * Checks if animation data is available from remote config.
     */
    suspend fun isAnimationDataAvailable(): Boolean = withContext(Dispatchers.IO) {
        try {
            val jsonString = remoteConfigHelper.getString(ANIMATION_JSON_KEY)
            val isAvailable = jsonString.isNotBlank()
            BatteryLogger.d(TAG, "Animation data available: $isAvailable")
            isAvailable
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Error checking animation data availability", e)
            false
        }
    }
}
