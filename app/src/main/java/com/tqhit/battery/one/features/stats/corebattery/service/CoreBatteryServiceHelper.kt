package com.tqhit.battery.one.features.stats.corebattery.service

import android.app.ActivityManager
import android.content.Context
import android.content.Intent
import android.os.Build
import android.util.Log
import com.tqhit.battery.one.utils.BatteryLogger
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Helper class for managing the CoreBatteryStatsService.
 * Provides centralized control for starting and stopping the service,
 * and checking its running status.
 */
@Singleton
class CoreBatteryServiceHelper @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    companion object {
        private const val TAG = "CoreBatteryServiceHelper"
    }
    
    /**
     * Checks if the CoreBatteryStatsService is currently running.
     *
     * @return true if the service is active, false otherwise
     */
    fun isServiceRunning(): Boolean {
        val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        
        @Suppress("DEPRECATION")
        val runningServices = activityManager.getRunningServices(Integer.MAX_VALUE)
        
        val isRunning = runningServices.any { serviceInfo ->
            serviceInfo.service.className == CoreBatteryStatsService::class.java.name
        }
        
        BatteryLogger.d(TAG, "CoreBatteryStatsService running status: $isRunning")
        return isRunning
    }
    
    /**
     * Starts the CoreBatteryStatsService with monitoring action.
     * Handles foreground service start for Android O+ and logs the action.
     */
    fun startService() {
        BatteryLogger.d(TAG, "Starting CoreBatteryStatsService")
        
        val intent = Intent(context, CoreBatteryStatsService::class.java).apply {
            action = CoreBatteryStatsService.ACTION_START_MONITORING
        }
        
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                // Start as foreground service for Android O+
                context.startForegroundService(intent)
                BatteryLogger.d(TAG, "CoreBatteryStatsService started as foreground service")
            } else {
                // Start as regular service for older versions
                context.startService(intent)
                BatteryLogger.d(TAG, "CoreBatteryStatsService started as regular service")
            }
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Failed to start CoreBatteryStatsService", e)
        }
    }
    
    /**
     * Stops the CoreBatteryStatsService by sending a stop monitoring action.
     * Logs the action for debugging purposes.
     */
    fun stopService() {
        BatteryLogger.d(TAG, "Stopping CoreBatteryStatsService")
        
        val intent = Intent(context, CoreBatteryStatsService::class.java).apply {
            action = CoreBatteryStatsService.ACTION_STOP_MONITORING
        }
        
        try {
            context.startService(intent)
            BatteryLogger.d(TAG, "Stop command sent to CoreBatteryStatsService")
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Failed to stop CoreBatteryStatsService", e)
        }
    }
    
    /**
     * Restarts the CoreBatteryStatsService by stopping and then starting it.
     * Useful for applying configuration changes or recovering from errors.
     */
    fun restartService() {
        BatteryLogger.d(TAG, "Restarting CoreBatteryStatsService")
        stopService()
        
        // Small delay to ensure service stops before restarting
        android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
            startService()
        }, 1000)
    }
}
