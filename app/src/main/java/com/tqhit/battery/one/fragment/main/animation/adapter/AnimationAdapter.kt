package com.tqhit.battery.one.fragment.main.animation.adapter

import android.content.Context
import android.content.Intent
import android.graphics.Rect
import android.graphics.drawable.Drawable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.ViewHolder
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.request.target.Target
import com.google.android.material.imageview.ShapeableImageView
import com.tqhit.battery.one.R
import com.tqhit.battery.one.fragment.main.animation.data.AnimationItem
import com.facebook.shimmer.ShimmerFrameLayout
import com.tqhit.battery.one.databinding.ItemAnimationBinding
import com.tqhit.battery.one.activity.animation.AnimationActivity
import com.tqhit.battery.one.dialog.utils.LoadingDialog
import com.tqhit.battery.one.dialog.utils.PostAnimationDialog
import com.tqhit.battery.one.service.ChargingOverlayService
import com.tqhit.battery.one.utils.BatteryLogger
import com.tqhit.battery.one.utils.DateTimeUtils
import com.tqhit.battery.one.utils.OverlayPermissionUtils
import com.tqhit.battery.one.utils.VideoUtils
import com.tqhit.battery.one.utils.PreloadingMonitor
import com.tqhit.battery.one.viewmodel.animation.AnimationViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import androidx.core.net.toUri
import com.tqhit.battery.one.repository.AppRepository

class AnimationAdapter(
    private val parentContext: Context,
    private var items: List<AnimationItem>,
    private val animationViewModel: AnimationViewModel,
    private val appRepository: AppRepository,
    private val videoUtils: VideoUtils,
    private val preloadingMonitor: PreloadingMonitor
) : RecyclerView.Adapter<AnimationViewHolder>() {
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): AnimationViewHolder {
        val binding = ItemAnimationBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return AnimationViewHolder(binding)
    }

    override fun onBindViewHolder(holder: AnimationViewHolder, position: Int) {
        holder.bind(items[position], animationViewModel, appRepository, parentContext, videoUtils, preloadingMonitor)
    }

    override fun getItemCount(): Int = items.size

    fun updateItems(newItems: List<AnimationItem>) {
        this.items = newItems
        notifyDataSetChanged()
    }
}

class AnimationViewHolder(private val binding: ItemAnimationBinding) : ViewHolder(binding.root) {
    fun bind(
        item: AnimationItem,
        animationViewModel: AnimationViewModel,
        appRepository: AppRepository,
        parentContext: Context,
        videoUtils: VideoUtils,
        preloadingMonitor: PreloadingMonitor
    ) {
        binding.shimmerLayout.startShimmer()
        binding.shimmerLayout.visibility = View.VISIBLE
        Glide.with(itemView)
            .load(item.thumbnail)
            .centerCrop()
            .listener(object : com.bumptech.glide.request.RequestListener<Drawable> {
                override fun onLoadFailed(
                    e: GlideException?,
                    model: Any?,
                    target: Target<Drawable?>,
                    isFirstResource: Boolean
                ): Boolean {
                    binding.shimmerLayout.stopShimmer()
                    binding.shimmerLayout.visibility = View.GONE
                    return false
                }

                override fun onResourceReady(
                    resource: Drawable,
                    model: Any,
                    target: Target<Drawable?>?,
                    dataSource: DataSource,
                    isFirstResource: Boolean
                ): Boolean {
                    binding.shimmerLayout.stopShimmer()
                    binding.shimmerLayout.visibility = View.GONE
                    return false
                }
            })
            .into(binding.cardImage)
        binding.lockBtn.visibility = if (item.isPremium) View.VISIBLE else View.GONE

        binding.root.setOnClickListener {
            val context = binding.root.context
            val intent = Intent(context, AnimationActivity::class.java).apply {
                putExtra("video_url", item.mediaOriginal)
            }
            context.startActivity(intent)
        }

        val isExpired = animationViewModel.isExpired(item.mediaOriginal)
        val isApplied = animationViewModel.isApplied(item.mediaOriginal)
        binding.applyButton.background = ContextCompat.getDrawable(itemView.context, R.drawable.colorr_button)
        if (!isExpired) {
            binding.applyBlock.visibility = View.VISIBLE
            binding.iconAd.visibility = View.GONE
            binding.textBtn.text = itemView.context.getString(R.string.apply)
            binding.textBtn.setTextColor(getThemeColor(R.attr.grey, itemView.context))
            if (isApplied) {
                binding.applyButton.background = ContextCompat.getDrawable(itemView.context, R.drawable.grey_button)
                binding.textBtn.setTextColor(getThemeColor(R.attr.black, itemView.context))
                binding.textBtn.text = itemView.context.getString(R.string.applied)
            }
        } else {
            binding.applyBlock.visibility = View.GONE
            animationViewModel.clearAnimation(item.mediaOriginal)
        }

        binding.applyButton.setOnClickListener {
            if (animationViewModel.isApplied(item.mediaOriginal)) return@setOnClickListener
            val context = binding.root.context

            BatteryLogger.d("AnimationAdapter", "Apply button clicked for animation: ${item.mediaOriginal}")

            // Check overlay permission using centralized utility
            if (!OverlayPermissionUtils.isOverlayPermissionGranted(context)) {
                BatteryLogger.d("AnimationAdapter", "Overlay permission not granted, showing permission dialog")
                OverlayPermissionUtils.showOverlayPermissionDialog(
                    context = context,
                    onConfirm = {
                        OverlayPermissionUtils.openOverlayPermissionSettings(context)
                    },
                    onCancel = {
                        BatteryLogger.d("AnimationAdapter", "User cancelled overlay permission request")
                    }
                )
                return@setOnClickListener
            }
            BatteryLogger.d("AnimationAdapter", "Overlay permission granted, proceeding with animation application")

            // Check if video is preloaded for faster loading
            (parentContext as? androidx.lifecycle.LifecycleOwner)?.lifecycleScope?.launch {
                val isPreloaded = videoUtils.isVideoPreloaded(item.mediaOriginal)
                val loadingMessage = if (isPreloaded) {
                    context.getString(R.string.applying_animation)
                } else {
                    context.getString(R.string.saving_video)
                }

                // Log cache hit/miss for monitoring
                preloadingMonitor.logAnimationPreloadAttempt(item.mediaOriginal, isPreloaded)

                BatteryLogger.d("AnimationAdapter", "Video preloaded status for ${item.mediaOriginal}: $isPreloaded")

                // Show loading dialog
                val loadingDialog = LoadingDialog(context, loadingMessage)
                loadingDialog.show()

                try {
                    // Use enhanced video utils that checks for preloaded files first
                    val destFile = videoUtils.downloadAndSaveVideoEnhanced(item.mediaOriginal)
                    appRepository.setVideoPath(destFile.absolutePath)
                    appRepository.setAnimationOverlayEnabled(true)
                    loadingDialog.dismiss()

                    val successMessage = if (isPreloaded) {
                        "Animation applied using preloaded file: ${item.mediaOriginal}"
                    } else {
                        "Animation downloaded and applied: ${item.mediaOriginal}"
                    }
                    BatteryLogger.d("AnimationAdapter", successMessage)
                    Toast.makeText(context, context.getString(R.string.video_saved), Toast.LENGTH_SHORT).show()

                    animationViewModel.setTrialEndTime(item.mediaOriginal)
                    animationViewModel.setApplied(item.mediaOriginal)
                    (binding.root.parent as? RecyclerView)?.adapter?.notifyDataSetChanged()

                    // Show post-animation dialog since overlay permission is already granted
                    BatteryLogger.d("AnimationAdapter", "Showing post-animation instruction dialog")
                    PostAnimationDialog.showDialog(context) {
                        BatteryLogger.d("AnimationAdapter", "Post-animation dialog dismissed")
                    }
                } catch (e: Exception) {
                    loadingDialog.dismiss()
                    BatteryLogger.e("AnimationAdapter", "Error applying animation: ${item.mediaOriginal}", e)
                    Toast.makeText(context, context.getString(R.string.error_accessing_file), Toast.LENGTH_SHORT).show()
                }
            }
        }
    }
}

private fun getThemeColor(attr: Int, context: Context): Int {
    val typedValue = android.util.TypedValue()
    val theme = context.theme
    theme.resolveAttribute(attr, typedValue, true)
    return typedValue.data
}

// Utility class for grid spacing
class GridSpacingItemDecoration(
    private val spanCount: Int,
    private val spacing: Int
) : RecyclerView.ItemDecoration() {
    override fun getItemOffsets(
        outRect: Rect,
        view: View,
        parent: RecyclerView,
        state: RecyclerView.State
    ) {
        val position = parent.getChildAdapterPosition(view) // item position
        val column = position % spanCount // item column

        // Only add spacing to the right of the left item and to the left of the right item
        outRect.left = if (column == 0) 0 else spacing / 2
        outRect.right = if (column == spanCount - 1) 0 else spacing / 2
        // Only add top spacing for rows except the first
        outRect.top = if (position >= spanCount) spacing else 0
        // No bottom spacing
        outRect.bottom = 0
    }
}