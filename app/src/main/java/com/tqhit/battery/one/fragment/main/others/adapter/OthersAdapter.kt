package com.tqhit.battery.one.fragment.main.others.adapter

import android.util.Log
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.tqhit.battery.one.databinding.ItemLayoutOthersBinding
import com.tqhit.battery.one.fragment.main.others.data.OthersItemData

/**
 * RecyclerView adapter for the Others fragment card-based layout.
 * Handles the display of Charge/Discharge, Battery Health, and Settings cards.
 *
 * Following the established adapter pattern from the codebase with proper
 * click handling and DiffUtil for efficient updates.
 */
class OthersAdapter(
    private val onItemClick: (OthersItemData) -> Unit
) : ListAdapter<OthersItemData, OthersAdapter.OthersViewHolder>(OthersDiffCallback()) {

    companion object {
        private const val TAG = "OthersAdapter"
    }

    override fun onCreateViewHolder(parent: <PERSON><PERSON>roup, viewType: Int): OthersViewHolder {
        Log.d(TAG, "OTHERS_ADAPTER: Creating ViewHolder")
        val binding = ItemLayoutOthersBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return OthersViewHolder(binding, onItemClick)
    }

    override fun onBindViewHolder(holder: OthersViewHolder, position: Int) {
        val item = getItem(position)
        Log.d(TAG, "OTHERS_ADAPTER: Binding item at position $position - ${item.title}")
        holder.bind(item)
    }

    /**
     * ViewHolder for Others fragment items.
     * Handles the binding of data to the card layout and click events.
     */
    class OthersViewHolder(
        private val binding: ItemLayoutOthersBinding,
        private val onItemClick: (OthersItemData) -> Unit
    ) : RecyclerView.ViewHolder(binding.root) {

        companion object {
            private const val TAG = "OthersViewHolder"
        }

        fun bind(item: OthersItemData) {
            Log.d(TAG, "OTHERS_VIEWHOLDER: Binding item ${item.id} - ${item.title}")

            // Set the card content
            binding.itemTitle.text = item.title
            binding.itemDesc.text = item.description
            binding.itemIcon.setImageResource(item.iconResId)

            // Set enabled state
            binding.root.isEnabled = item.isEnabled
            binding.root.alpha = if (item.isEnabled) 1.0f else 0.6f

            // Set click listener
            binding.root.setOnClickListener {
                if (item.isEnabled) {
                    Log.d(TAG, "OTHERS_VIEWHOLDER: Item clicked - ${item.id}")
                    onItemClick(item)
                } else {
                    Log.d(TAG, "OTHERS_VIEWHOLDER: Item disabled, ignoring click - ${item.id}")
                }
            }

            Log.d(TAG, "OTHERS_VIEWHOLDER: Successfully bound item ${item.id}")
        }
    }

    /**
     * DiffUtil callback for efficient RecyclerView updates.
     * Compares items by ID and content for optimal performance.
     */
    private class OthersDiffCallback : DiffUtil.ItemCallback<OthersItemData>() {
        override fun areItemsTheSame(oldItem: OthersItemData, newItem: OthersItemData): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: OthersItemData, newItem: OthersItemData): Boolean {
            return oldItem == newItem
        }
    }
}