<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <LinearLayout
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:animateLayoutChanges="true">
            <LinearLayout
                android:orientation="vertical"
                android:id="@+id/indent_top"
                android:layout_width="match_parent"
                android:layout_height="match_parent"/>
            <LinearLayout
                android:orientation="vertical"
                android:id="@+id/main"
                android:background="@drawable/white_block"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="3dp"
                android:animateLayoutChanges="true"
                android:layout_marginStart="9dp"
                android:layout_marginEnd="9dp">
                <TextView
                    android:textSize="22sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:gravity="start"
                    android:id="@+id/textView4"
                    android:focusableInTouchMode="true"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="7dp"
                    android:text="@string/calibration_going"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    android:layout_marginStart="14dp"
                    android:layout_marginEnd="14dp"/>
                <LinearLayout
                    android:orientation="vertical"
                    android:id="@+id/text5"
                    android:background="@drawable/grey_block"
                    android:focusable="true"
                    android:visibility="gone"
                    android:clickable="true"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginTop="7dp"
                    android:layout_marginBottom="1dp"
                    android:layout_marginStart="8dp"
                    android:layout_marginEnd="8dp">
                    <TextView
                        android:textSize="14sp"
                        android:textColor="?attr/black"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="7dp"
                        android:layout_marginBottom="7dp"
                        android:text="@string/disconnect_charge"
                        android:layout_marginStart="9dp"
                        android:layout_marginEnd="7dp"/>
                </LinearLayout>
                <LinearLayout
                    android:orientation="vertical"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginTop="7dp"
                    android:layout_marginBottom="8dp"
                    android:layout_marginStart="8dp"
                    android:layout_marginEnd="8dp">
                    <LinearLayout
                        android:gravity="center"
                        android:background="@drawable/grey_block_line_up"
                        android:focusable="true"
                        android:clickable="true"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent">
                        <TextView
                            android:textSize="14sp"
                            android:textColor="?attr/black"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="12dp"
                            android:layout_marginBottom="12dp"
                            android:text="@string/device"
                            android:layout_marginStart="10dp"/>
                        <TextView
                            android:textSize="14sp"
                            android:textColor="?attr/black"
                            android:ellipsize="marquee"
                            android:id="@+id/device_name"
                            android:focusableInTouchMode="true"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="12dp"
                            android:layout_marginBottom="12dp"
                            android:text="@string/not_identified"
                            android:singleLine="true"
                            android:layout_weight="1"
                            android:marqueeRepeatLimit="marquee_forever"
                            android:textAlignment="viewEnd"
                            android:layout_marginStart="4dp"
                            android:layout_marginEnd="10dp"/>
                    </LinearLayout>
                    <LinearLayout
                        android:gravity="center"
                        android:background="@drawable/grey_block_line"
                        android:focusable="true"
                        android:clickable="true"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5dp">
                        <TextView
                            android:textSize="14sp"
                            android:textColor="?attr/black"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="12dp"
                            android:layout_marginBottom="12dp"
                            android:text="@string/discharging"
                            android:layout_marginStart="10dp"/>
                        <TextView
                            android:textSize="14sp"
                            android:textColor="?attr/black"
                            android:ellipsize="marquee"
                            android:id="@+id/discharging"
                            android:focusableInTouchMode="true"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="12dp"
                            android:layout_marginBottom="12dp"
                            android:text="@string/not_identified"
                            android:singleLine="true"
                            android:layout_weight="1"
                            android:marqueeRepeatLimit="marquee_forever"
                            android:textAlignment="viewEnd"
                            android:layout_marginStart="4dp"
                            android:layout_marginEnd="10dp"/>
                    </LinearLayout>
                    <LinearLayout
                        android:gravity="center"
                        android:background="@drawable/grey_block_line"
                        android:focusable="true"
                        android:clickable="true"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginTop="5dp">
                        <TextView
                            android:textSize="14sp"
                            android:textColor="?attr/black"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="12dp"
                            android:layout_marginBottom="12dp"
                            android:text="@string/polarity"
                            android:layout_marginStart="10dp"/>
                        <TextView
                            android:textSize="14sp"
                            android:textColor="?attr/black"
                            android:ellipsize="marquee"
                            android:id="@+id/polarity"
                            android:focusableInTouchMode="true"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="12dp"
                            android:layout_marginBottom="12dp"
                            android:text="@string/not_identified"
                            android:singleLine="true"
                            android:layout_weight="1"
                            android:marqueeRepeatLimit="marquee_forever"
                            android:textAlignment="viewEnd"
                            android:layout_marginStart="4dp"
                            android:layout_marginEnd="10dp"/>
                    </LinearLayout>
                    <LinearLayout
                        android:gravity="center"
                        android:background="@drawable/grey_block_line"
                        android:focusable="true"
                        android:clickable="true"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginTop="5dp">
                        <TextView
                            android:textSize="14sp"
                            android:textColor="?attr/black"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="12dp"
                            android:layout_marginBottom="12dp"
                            android:text="@string/measurement_parameter"
                            android:layout_marginStart="10dp"/>
                        <TextView
                            android:textSize="14sp"
                            android:textColor="?attr/black"
                            android:ellipsize="marquee"
                            android:id="@+id/parameter"
                            android:focusableInTouchMode="true"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="12dp"
                            android:layout_marginBottom="12dp"
                            android:text="@string/not_identified"
                            android:singleLine="true"
                            android:layout_weight="1"
                            android:marqueeRepeatLimit="marquee_forever"
                            android:textAlignment="viewEnd"
                            android:layout_marginStart="4dp"
                            android:layout_marginEnd="10dp"/>
                    </LinearLayout>
                    <LinearLayout
                        android:gravity="center"
                        android:background="@drawable/grey_block_line_down"
                        android:focusable="true"
                        android:clickable="true"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginTop="5dp">
                        <TextView
                            android:textSize="14sp"
                            android:textColor="?attr/black"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="12dp"
                            android:layout_marginBottom="12dp"
                            android:text="@string/desing_capacity"
                            android:layout_marginStart="10dp"/>
                        <TextView
                            android:textSize="14sp"
                            android:textColor="?attr/black"
                            android:ellipsize="marquee"
                            android:id="@+id/capacity"
                            android:focusableInTouchMode="true"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="12dp"
                            android:layout_marginBottom="12dp"
                            android:text="@string/not_identified"
                            android:singleLine="true"
                            android:layout_weight="1"
                            android:marqueeRepeatLimit="marquee_forever"
                            android:textAlignment="viewEnd"
                            android:layout_marginStart="4dp"
                            android:layout_marginEnd="10dp"/>
                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>
            <LinearLayout
                android:orientation="vertical"
                android:background="@drawable/white_block"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:layout_marginBottom="120dp"
                android:animateLayoutChanges="true"
                android:layout_marginStart="9dp"
                android:layout_marginEnd="9dp">
                <LinearLayout
                    android:orientation="vertical"
                    android:background="@drawable/grey_block"
                    android:focusable="true"
                    android:clickable="true"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginTop="8dp"
                    android:layout_marginBottom="8dp"
                    android:layout_marginStart="8dp"
                    android:layout_marginEnd="8dp">
                    <TextView
                        android:textSize="14sp"
                        android:textColor="?attr/black"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="7dp"
                        android:layout_marginBottom="7dp"
                        android:text="@string/design_capacity_text_incorrect"
                        android:textAlignment="viewStart"
                        android:layout_marginStart="9dp"
                        android:layout_marginEnd="7dp"/>
                </LinearLayout>
                <RelativeLayout
                    android:gravity="center"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginBottom="8dp"
                    android:layout_marginStart="8dp"
                    android:layout_marginEnd="8dp">
                    <Button
                        android:gravity="top|center_horizontal"
                        android:id="@+id/change_capacity"
                        android:background="@drawable/grey_block"
                        android:longClickable="false"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:minWidth="0dp"
                        android:minHeight="0dp"
                        android:layout_alignTop="@+id/text_view_reset_charge"
                        android:layout_alignBottom="@+id/text_view_reset_charge"
                        android:layout_alignParentBottom="false"
                        style="@style/Widget.AppCompat.Button.Borderless"/>
                    <TextView
                        android:textSize="14sp"
                        android:textColor="?attr/black"
                        android:gravity="center"
                        android:id="@+id/text_view_reset_charge"
                        android:paddingTop="12.5dp"
                        android:paddingBottom="12.5dp"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/change_design_capacity"
                        android:paddingStart="10dp"
                        android:paddingEnd="10dp"
                        android:layout_marginStart="10dp"
                        android:layout_marginEnd="10dp"/>
                </RelativeLayout>
            </LinearLayout>
        </LinearLayout>
    </ScrollView>
    <com.google.android.material.progressindicator.CircularProgressIndicator
        android:id="@+id/progressbar2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="50dp"
        android:progress="100"
        android:indeterminate="true"
        android:layout_alignParentBottom="true"
        android:layout_marginEnd="30dp"
        android:layout_alignParentEnd="true"
        app:indicatorColor="?attr/grey_pressed"
        app:indicatorInset="0dp"
        app:indicatorSize="80dp"
        app:trackColor="?attr/white"
        app:trackCornerRadius="10dp"
        app:trackThickness="6dp"/>
    <RelativeLayout
        android:layout_gravity="center"
        android:id="@+id/button"
        android:background="@drawable/white_block_round"
        android:visibility="visible"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:layout_marginTop="20dp"
        android:layout_marginBottom="50dp"
        android:layout_alignParentBottom="true"
        android:layout_marginEnd="30dp"
        android:layout_alignParentEnd="true"
        android:stateListAnimator="@null"
        android:outlineProvider="background">
        <LinearLayout
            android:orientation="vertical"
            android:background="@drawable/white_block_round"
            android:visibility="visible"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_centerInParent="true"/>
        <Button
            android:id="@+id/start_main_activity"
            android:background="@drawable/button_static"
            android:visibility="visible"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_margin="8dp"
            android:layout_centerInParent="true"
            style="@style/Widget.AppCompat.Button.Borderless"/>
        <LinearLayout
            android:orientation="vertical"
            android:background="@drawable/ic_strelka"
            android:visibility="visible"
            android:layout_width="26dp"
            android:layout_height="20dp"
            android:layout_centerInParent="true"
            android:scaleX="-1"/>
    </RelativeLayout>
</RelativeLayout>
</LinearLayout>
